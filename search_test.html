<!DOCTYPE html>
<html>
<head>
    <title>Search Test</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; line-height: 1.6; }
        h1 { color: #333; }
        .container { max-width: 800px; margin: 0 auto; }
        .search-box { margin-bottom: 20px; }
        input[type="text"] { padding: 8px; width: 300px; }
        button { padding: 8px 16px; background: #4CAF50; color: white; border: none; cursor: pointer; }
        button:hover { background: #45a049; }
        #results { border: 1px solid #ddd; padding: 15px; min-height: 100px; margin-top: 20px; }
        .result-item { padding: 10px; border-bottom: 1px solid #eee; }
        .result-name { font-weight: bold; }
        .result-email { color: #666; font-size: 14px; }
        .error { color: red; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Search Test Page</h1>
        <p>This is a simple test page to verify the search API is working correctly.</p>
        
        <div class="search-box">
            <input type="text" id="search-input" placeholder="Type to search...">
            <button id="search-button">Search</button>
        </div>
        
        <div>
            <h3>Debug Options:</h3>
            <a href="debug_search.php?q=a" target="_blank">Run debug search with query 'a'</a><br>
            <a href="test_search.php" target="_blank">Open test search diagnostics</a>
        </div>
        
        <div id="results">
            <p>Search results will appear here...</p>
        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('search-input');
        const searchButton = document.getElementById('search-button');
        const resultsDiv = document.getElementById('results');
        
        // Function to perform the search
        function performSearch() {
            const query = searchInput.value.trim();
            
            if (query === '') {
                resultsDiv.innerHTML = '<p>Please enter a search term</p>';
                return;
            }
            
            // Display searching message
            resultsDiv.innerHTML = '<p>Searching...</p>';
            
            // Create request
            const xhr = new XMLHttpRequest();
            xhr.open('GET', 'live_search.php?query=' + encodeURIComponent(query), true);
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    console.log('Status:', xhr.status);
                    console.log('Response:', xhr.responseText);
                    
                    if (xhr.status === 200) {
                        try {
                            const data = JSON.parse(xhr.responseText);
                            displayResults(data);
                        } catch (e) {
                            resultsDiv.innerHTML = '<p class="error">Error parsing response: ' + e.message + '</p>' +
                                '<pre>' + xhr.responseText + '</pre>';
                        }
                    } else {
                        resultsDiv.innerHTML = '<p class="error">Error: ' + xhr.status + '</p>';
                    }
                }
            };
            
            xhr.onerror = function() {
                resultsDiv.innerHTML = '<p class="error">Network error occurred</p>';
            };
            
            xhr.send();
        }
        
        // Function to display the results
        function displayResults(data) {
            if (!data || data.length === 0) {
                resultsDiv.innerHTML = '<p>No results found</p>';
                return;
            }
            
            let html = '<h2>Search Results (' + data.length + ')</h2>';
            
            data.forEach(function(item) {
                html += '<div class="result-item">' +
                    '<div class="result-name">' + item.name + ' (' + item.type + ')</div>' +
                    '<div class="result-email">' + (item.email || 'No email') + '</div>' +
                    '<div>ID: ' + item.id + '</div>' +
                '</div>';
            });
            
            resultsDiv.innerHTML = html;
        }
        
        // Add event listeners
        searchButton.addEventListener('click', performSearch);
        
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
    });
    </script>
</body>
</html> 