<?php
session_start();
require_once 'config.php';

// Set page title
$pageTitle = 'CLIENT LIST';

// Use a different function name to avoid conflicts
function getClientsWithPolicyData() {
    global $conn;
    $clients = [];
    
    // First get basic client information
    $queryClients = "SELECT * FROM clients";
    $resultClients = mysqli_query($conn, $queryClients);
    
    if (!$resultClients) {
        die("Client query failed: " . mysqli_error($conn));
    }
    
    while ($client = mysqli_fetch_assoc($resultClients)) {
        // For each client, count their policies separately to avoid GROUP BY issues
        $policyQuery = "SELECT 
            policy_id, 
            status 
            FROM policies 
            WHERE client_id = '" . mysqli_real_escape_string($conn, $client['client_id']) . "'";
        
        $policyResult = mysqli_query($conn, $policyQuery);
        
        if (!$policyResult) {
            die("Policy query failed: " . mysqli_error($conn));
        }
        
        // Initialize counters
        $client['total_policies'] = 0;
        $client['active_policies'] = 0;
        $client['pending_policies'] = 0;
        
        // Count policies by status
        while ($policy = mysqli_fetch_assoc($policyResult)) {
            $client['total_policies']++;
            
            // Use case-insensitive comparison
            if (strcasecmp(trim($policy['status']), 'Active') === 0) {
                $client['active_policies']++;
            } elseif (strcasecmp(trim($policy['status']), 'Pending') === 0) {
                $client['pending_policies']++;
            }
        }
        
        $clients[] = $client;
    }
    
    return $clients;
}

// Get all clients with our new function
$clients = getClientsWithPolicyData();

// Include the layout header
include 'layout.php';

// Display any error messages
if (isset($_GET['error'])) {
    echo '<div class="alert alert-danger alert-dismissible fade show" role="alert">
            ' . htmlspecialchars($_GET['error']) . '
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
          </div>';
}
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h2 class="text-dark font-weight-bold">Clients</h2>
    <a href="client_add.php" class="btn btn-success">
        <i class="fas fa-plus mr-2"></i> Add New Client
    </a>
</div>

<div class="card shadow-sm">
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr class="bg-light">
                        <th class="px-4 py-3">Name</th>
                        <th class="px-4 py-3">Email</th>
                        <th class="px-4 py-3">Phone</th>
                        <th class="px-4 py-3">Status</th>
                        <th class="px-4 py-3 text-center">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($clients)): ?>
                        <tr>
                            <td colspan="5" class="text-center py-4">No clients found</td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($clients as $client): ?>
                            <tr data-client-id="<?php echo $client['client_id']; ?>">
                                <td class="px-4 py-3"><?php echo htmlspecialchars($client['name']); ?></td>
                                <td class="px-4 py-3"><?php echo htmlspecialchars($client['email']); ?></td>
                                <td class="px-4 py-3"><?php echo htmlspecialchars($client['phone_number']); ?></td>
                                <td class="px-4 py-3">
                                    <?php 
                                    // Make sure the keys exist with default values to avoid warnings
                                    $activePolicies = isset($client['active_policies']) ? (int)$client['active_policies'] : 0;
                                    $pendingPolicies = isset($client['pending_policies']) ? (int)$client['pending_policies'] : 0;
                                    $totalPolicies = isset($client['total_policies']) ? (int)$client['total_policies'] : 0;
                                    
                                    // Determine client status based on policy statuses
                                    if ($totalPolicies > 0) {
                                        if ($activePolicies > 0) {
                                            echo '<span class="badge badge-success">Active</span>';
                                        } elseif ($pendingPolicies > 0) {
                                            echo '<span class="badge badge-warning">Pending</span>';
                                        } else {
                                            echo '<span class="badge badge-info">Other</span>';
                                        }
                                    } else {
                                        echo '<span class="badge badge-secondary">No Policies</span>';
                                    }
                                    ?>
                                    
                                    <?php if ($totalPolicies > 0): ?>
                                        <div class="policy-details mt-1">
                                            <?php if ($activePolicies > 0): ?>
                                                <span class="badge badge-outline-success">
                                                    <?php echo $activePolicies; ?> Active
                                                </span>
                                            <?php endif; ?>
                                            
                                            <?php if ($pendingPolicies > 0): ?>
                                                <span class="badge badge-outline-warning ml-1">
                                                    <?php echo $pendingPolicies; ?> Pending
                                                </span>
                                            <?php endif; ?>
                                            
                                            <?php 
                                            $otherPolicies = $totalPolicies - $activePolicies - $pendingPolicies;
                                            if ($otherPolicies > 0): 
                                            ?>
                                                <span class="badge badge-outline-info ml-1">
                                                    <?php echo $otherPolicies; ?> Other
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td class="px-4 py-3 text-center">
                                    <div class="action-buttons">
                                        <a href="client_view.php?id=<?php echo $client['client_id']; ?>" class="action-btn view">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="client_edit.php?id=<?php echo $client['client_id']; ?>" class="action-btn edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button class="action-btn delete" 
                                                data-client-id="<?php echo $client['client_id']; ?>"
                                                data-client-name="<?php echo htmlspecialchars($client['name']); ?>">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>  
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1" role="dialog" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteConfirmModalLabel">Confirm Delete</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete <span id="clientNameToDelete"></span>?</p>
                <p class="text-danger">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete</button>
            </div>
        </div>
    </div>
</div>

<style>
    .table {
        font-size: 0.95rem;
    }
    .table thead tr {
        border-bottom: 2px solid #e0e0e0;
    }
    .table td, .table th {
        vertical-align: middle;
        border-top: 1px solid #e9ecef;
    }
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        line-height: 1.5;
        border-radius: 0.2rem;
    }
    .card {
        border: none;
        border-radius: 10px;
        overflow: hidden;
    }
    .bg-light {
        background-color: #f8f9fa !important;
    }

    /* Action buttons styling */
    .action-buttons {
        display: flex;
        gap: 8px;
        justify-content: center;
    }

    .action-btn {
        width: 48px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        border: none;
        color: white;
        transition: all 0.2s ease;
        text-decoration: none;
        cursor: pointer;
    }

    .action-btn:hover {
        transform: translateY(-1px);
        color: white;
        text-decoration: none;
        opacity: 0.9;
    }

    .action-btn i {
        font-size: 13px;
    }

    .action-btn.view {
        background-color: #17a2b8;
    }

    .action-btn.edit {
        background-color: #6c757d;
    }

    .action-btn.delete {
        background-color: #dc3545;
    }

    /* Status badge styling */
    .badge {
        font-size: 0.85rem;
        padding: 0.4em 0.8em;
        font-weight: 500;
        border-radius: 4px;
    }

    .badge-success {
        background-color: #e8f5e9;
        color: #2e7d32;
    }

    .badge-secondary {
        background-color: #f5f5f5;
        color: #616161;
    }

    .badge-warning {
        background-color: #fff3e0;
        color: #e65100;
    }

    .badge-info {
        background-color: #e3f2fd;
        color: #0d47a1;
    }

    .gap-2 {
        gap: 0.5rem !important;
    }
    .modal-content {
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .modal-header {
        border-bottom: none;
        padding: 20px 25px;
    }
    .modal-title {
        font-size: 24px;
        font-weight: 600;
        color: #2c3e50;
    }
    .modal-body {
        padding: 20px 25px;
    }
    .modal-body p {
        margin-bottom: 10px;
        font-size: 16px;
    }
    .modal-body .text-danger {
        color: #dc3545;
        font-size: 14px;
    }
    .modal-footer {
        border-top: none;
        padding: 15px 25px 20px;
    }
    .btn-secondary {
        background-color: #6c757d;
        border: none;
        padding: 8px 20px;
        font-weight: 500;
        border-radius: 6px;
    }
    .btn-danger {
        background-color: #dc3545;
        border: none;
        padding: 8px 20px;
        font-weight: 500;
        border-radius: 6px;
    }
    .close {
        font-size: 1.5rem;
        opacity: 0.5;
        transition: opacity 0.2s;
    }
    .close:hover {
        opacity: 1;
    }

    /* New outline badge styles */
    .policy-details {
        margin-top: 5px;
    }
    
    .badge-outline-success {
        background-color: transparent;
        border: 1px solid #28a745;
        color: #28a745;
        font-size: 0.75rem;
    }
    
    .badge-outline-warning {
        background-color: transparent;
        border: 1px solid #ffc107;
        color: #e65100;
        font-size: 0.75rem;
    }
    
    .badge-outline-info {
        background-color: transparent;
        border: 1px solid #17a2b8;
        color: #0d47a1;
        font-size: 0.75rem;
    }
    
    .mt-1 {
        margin-top: 0.25rem;
    }
</style>

<script>
let clientIdToDelete = null;

function showDeleteConfirmation(clientId, clientName) {
    clientIdToDelete = clientId;
    document.getElementById('clientNameToDelete').textContent = clientName;
    $('#deleteConfirmModal').modal('show');
}

document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
    if (clientIdToDelete) {
        // Send delete request
        fetch('delete_client.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                client_id: clientIdToDelete
            })
        })
        .then(response => response.json())
        .then(data => {
            // Close modal
            $('#deleteConfirmModal').modal('hide');
            
            if (data.success) {
                // Remove the row from the table
                document.querySelector(`tr[data-client-id="${clientIdToDelete}"]`).remove();
                // Redirect to refresh the page instead of showing alert
                window.location.reload();
            } else {
                console.error('Failed to delete client:', data.message);
                window.location.reload();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            // Close modal and refresh the page
            $('#deleteConfirmModal').modal('hide');
            window.location.reload();
        });
    }
});

// Update the delete button in the table to use the modal
document.querySelectorAll('.delete').forEach(button => {
    button.addEventListener('click', function(e) {
        e.preventDefault();
        const clientId = this.getAttribute('data-client-id');
        const clientName = this.getAttribute('data-client-name');
        showDeleteConfirmation(clientId, clientName);
    });
});
</script>

<?php include 'layout_footer.php'; ?> 