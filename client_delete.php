<?php
session_start();
require_once 'config.php';

// Check if ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['error'] = 'Invalid client ID';
    header('Location: clients.php');
    exit;
}

$clientId = (int)$_GET['id'];

try {
    // Get client details for logging
    $stmt = $pdo->prepare("SELECT name FROM clients WHERE id = ?");
    $stmt->execute([$clientId]);
    $client = $stmt->fetch();

    if (!$client) {
        throw new Exception('Client not found');
    }

    // Delete the client
    $stmt = $pdo->prepare("DELETE FROM clients WHERE id = ?");
    $stmt->execute([$clientId]);

    // Log the activity
    logActivity($_SESSION['user_id'], 'Deleted client', "Deleted client: {$client['name']}");

    $_SESSION['success'] = 'Client deleted successfully';
} catch (Exception $e) {
    $_SESSION['error'] = 'Error deleting client: ' . $e->getMessage();
}

header('Location: clients.php');
exit; 