<?php
session_start();
require_once 'config.php';

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Check if client ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header('Location: clients.php?error=Invalid client ID');
    exit;
}

$clientId = $_GET['id'];

// Try to get the client
$client = getClientById($clientId);

// If client not found, redirect with error
if (!$client) {
    header('Location: clients.php?error=Client not found');
    exit;
}

// Get all agents for dropdown
$agents = getAllAgents();

// Get current assigned agent if any
$currentAgentId = null;
try {
    // Check if agent_client table exists
    $tableCheck = $pdo->query("SHOW TABLES LIKE 'agent_client'");
    if ($tableCheck->rowCount() > 0) {
        $stmt = $pdo->prepare("SELECT agent_id FROM agent_client WHERE client_id = ?");
        $stmt->execute([$clientId]);
        $agentResult = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($agentResult) {
            $currentAgentId = $agentResult['agent_id'];
        }
    } else {
        // Log that table doesn't exist
        error_log("Warning: agent_client table does not exist in the database");
    }
} catch (PDOException $e) {
    // Log the error but continue execution
    error_log("Error when querying agent_client table: " . $e->getMessage());
}

// Handle form submission
$errors = [];
$warnings = [];
$success = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate required fields
    $requiredFields = ['name', 'ic_number', 'gender', 'date_of_birth', 'phone_number', 'email', 'address'];
    foreach ($requiredFields as $field) {
        if (empty($_POST[$field])) {
            $errors[] = ucfirst(str_replace('_', ' ', $field)) . ' is required';
        }
    }

    if (empty($errors)) {
        try {
            // Begin transaction
            $pdo->beginTransaction();
            
            // Update client data
            $sql = "UPDATE clients SET 
                name = :name,
                ic_number = :ic_number,
                client_number = :client_number,
                gender = :gender,
                date_of_birth = :date_of_birth,
                phone_number = :phone_number,
                email = :email,
                address = :address,
                marital_status = :marital_status,
                race = :race,
                religion = :religion,
                nationality = :nationality,
                occupation = :occupation,
                exact_duties = :exact_duties,
                nature_of_business = :nature_of_business,
                salary_yearly = :salary_yearly,
                company_name = :company_name,
                company_address = :company_address,
                weight = :weight,
                height = :height,
                smoker = :smoker,
                hospital_admission_history = :hospital_admission_history
                WHERE client_id = :client_id";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute([
                ':name' => $_POST['name'],
                ':ic_number' => $_POST['ic_number'],
                ':client_number' => $_POST['client_number'] ?? null,
                ':gender' => $_POST['gender'],
                ':date_of_birth' => $_POST['date_of_birth'],
                ':phone_number' => $_POST['phone_number'],
                ':email' => $_POST['email'],
                ':address' => $_POST['address'],
                ':marital_status' => $_POST['marital_status'] ?? null,
                ':race' => $_POST['race'] ?? null,
                ':religion' => $_POST['religion'] ?? null,
                ':nationality' => $_POST['nationality'] ?? null,
                ':occupation' => $_POST['occupation'] ?? null,
                ':exact_duties' => $_POST['exact_duties'] ?? null,
                ':nature_of_business' => $_POST['nature_of_business'] ?? null,
                ':salary_yearly' => $_POST['salary_yearly'] ?? null,
                ':company_name' => $_POST['company_name'] ?? null,
                ':company_address' => $_POST['company_address'] ?? null,
                ':weight' => $_POST['weight'] ?? null,
                ':height' => $_POST['height'] ?? null,
                ':smoker' => $_POST['smoker'] ?? null,
                ':hospital_admission_history' => $_POST['hospital_admission_history'] ?? null,
                ':client_id' => $clientId
            ]);
            
            // Update agent assignment
            
            
            // Commit transaction
            $pdo->commit();
            $success = true;
            
            // Refresh client data
            $client = getClientById($clientId);
            
            // Update current agent ID
            if (isset($_POST['agent_id'])) {
                $currentAgentId = $_POST['agent_id'];
            }
            
        } catch (Exception $e) {
            // Rollback transaction on error
            $pdo->rollBack();
            $errors[] = "Error updating client: " . $e->getMessage();
        }
    }
}

// Set page title
$pageTitle = 'EDIT CLIENT';

// Include layout header
include 'layout.php';
?>

<!-- Include Select2 CSS and JS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<style>
.detail-container {
    max-width: 1000px;
    margin-left: 400px;
    margin-bottom: 50px;
    padding: 30px;
    background: #fff;
    box-shadow: 0 0 20px rgba(0,0,0,0.05);
    border-radius: 15px;
}

.client-header {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 40px;
    text-align: center;
    color: #2c3e50;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.form-section {
    margin-bottom: 40px;
    background: #f8f9fa;
    padding: 30px;
    border-radius: 12px;
}

.section-title {
    font-weight: 600;
    font-size: 18px;
    color: #2c3e50;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e9ecef;
}

.detail-row {
    margin-bottom: 15px;
    display: flex;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    width: 180px;
    font-weight: 600;
    color: #495057;
    padding-top: 8px;
}

.detail-value {
    flex-grow: 1;
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

textarea.form-control {
    min-height: 100px;
    resize: vertical;
}

.btn-container {
    margin-top: 40px;
    display: flex;
    justify-content: space-between;
    gap: 10px;
}

.btn {
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #2c3e50;
    border: none;
    color: white;
}

.btn-primary:hover {
    background-color: #1e2a37;
    transform: translateY(-2px);
}

.btn-secondary {
    background-color: #6c757d;
    border: none;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
    transform: translateY(-2px);
}

.alert {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 8px;
}

.alert-danger {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    border: 1px solid #ffeeba;
    color: #856404;
}

.alert-success {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.required {
    color: #e74a3b;
    margin-left: 4px;
}

.strict-numbers-only {
    -moz-appearance: textfield;
}

.strict-numbers-only::-webkit-outer-spin-button,
.strict-numbers-only::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* Select2 Custom Styling */
.select2-container--default .select2-selection--single {
    height: 38px;
    padding: 5px 8px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 36px;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 28px;
    color: #495057;
}

.select2-dropdown {
    border: 1px solid #dee2e6;
    border-radius: 6px;
}

.select2-search--dropdown .select2-search__field {
    padding: 8px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #2c3e50;
}

.form-section .detail-row:last-child {
    margin-bottom: 0;
    border-bottom: none;
}

.form-section .detail-row .detail-value {
    padding: 0;
}

.form-section .detail-row {
    background: #f8f9fa;
    margin-bottom: 1rem;
    border: none;
}
</style>

<div class="detail-container">
    <?php if ($success): ?>
        <div class="alert alert-success">
            Client information updated successfully!
        </div>
    <?php endif; ?>
    
    <?php if (!empty($errors)): ?>
        <div class="alert alert-danger">
            <ul class="mb-0">
                <?php foreach ($errors as $error): ?>
                    <li><?php echo $error; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>
    
    <?php if (!empty($warnings)): ?>
        <div class="alert alert-warning">
            <ul class="mb-0">
                <?php foreach ($warnings as $warning): ?>
                    <li><?php echo $warning; ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>

    <div class="client-header">
        EDIT CLIENT
    </div>

    <form method="POST" class="form">
        <!-- Basic Information Section -->
        <div class="form-section">
            <div class="section-title">Basic Information</div>
            
            <div class="detail-row">
                <div class="detail-label">Name <span class="required">*</span></div>
                <div class="detail-value">
                    <input type="text" class="form-control" id="name" name="name" value="<?php echo htmlspecialchars($client['name']); ?>" required>
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">IC Number <span class="required">*</span></div>
                <div class="detail-value">
                    <input type="text" class="form-control strict-numbers-only" id="ic_number" name="ic_number" value="<?php echo htmlspecialchars($client['ic_number']); ?>" required pattern="[0-9]*" inputmode="numeric" onkeypress="return event.charCode >= 48 && event.charCode <= 57" maxlength="20">
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Client Number</div>
                <div class="detail-value">
                    <input type="text" class="form-control" id="client_number" name="client_number" value="<?php echo htmlspecialchars($client['client_number'] ?? ''); ?>">
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Gender <span class="required">*</span></div>
                <div class="detail-value">
                    <select class="form-control" id="gender" name="gender" required>
                        <option value="">Select Gender</option>
                        <option value="Male" <?php echo ($client['gender'] == 'Male') ? 'selected' : ''; ?>>Male</option>
                        <option value="Female" <?php echo ($client['gender'] == 'Female') ? 'selected' : ''; ?>>Female</option>
                        <option value="Other" <?php echo ($client['gender'] == 'Other') ? 'selected' : ''; ?>>Other</option>
                    </select>
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Date of Birth <span class="required">*</span></div>
                <div class="detail-value">
                    <input type="date" class="form-control" id="date_of_birth" name="date_of_birth" value="<?php echo htmlspecialchars($client['date_of_birth']); ?>" required>
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Phone Number <span class="required">*</span></div>
                <div class="detail-value">
                    <input type="text" class="form-control strict-numbers-only" id="phone_number" name="phone_number" value="<?php echo htmlspecialchars($client['phone_number']); ?>" required pattern="[0-9]*" inputmode="numeric" onkeypress="return event.charCode >= 48 && event.charCode <= 57" maxlength="15">
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Email <span class="required">*</span></div>
                <div class="detail-value">
                    <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($client['email']); ?>" required>
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Assign Agent</div>
                <div class="detail-value">
                    <select class="form-control" id="agent_id" name="agent_id">
                        <option value="">No Agent</option>
                        <?php foreach ($agents as $agent): ?>
                            <option value="<?php echo $agent['agent_id']; ?>" <?php echo ($currentAgentId == $agent['agent_id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($agent['name']); ?> (<?php echo htmlspecialchars($agent['agent_id']); ?>)
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Address <span class="required">*</span></div>
                <div class="detail-value">
                    <textarea class="form-control" id="address" name="address" rows="3" required><?php echo htmlspecialchars($client['address']); ?></textarea>
                </div>
            </div>
        </div>
        
        <!-- Personal Details Section -->
        <div class="form-section">
            <div class="section-title">Personal Details</div>
            
            <div class="detail-row">
                <div class="detail-label">Marital Status</div>
                <div class="detail-value">
                    <select class="form-control" id="marital_status" name="marital_status">
                        <option value="">Select Marital Status</option>
                        <option value="Single" <?php echo ($client['marital_status'] == 'Single') ? 'selected' : ''; ?>>Single</option>
                        <option value="Married" <?php echo ($client['marital_status'] == 'Married') ? 'selected' : ''; ?>>Married</option>
                        <option value="Divorced" <?php echo ($client['marital_status'] == 'Divorced') ? 'selected' : ''; ?>>Divorced</option>
                        <option value="Widowed" <?php echo ($client['marital_status'] == 'Widowed') ? 'selected' : ''; ?>>Widowed</option>
                    </select>
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Race</div>
                <div class="detail-value">
                    <input type="text" class="form-control" id="race" name="race" value="<?php echo htmlspecialchars($client['race'] ?? ''); ?>">
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Religion</div>
                <div class="detail-value">
                    <input type="text" class="form-control" id="religion" name="religion" value="<?php echo htmlspecialchars($client['religion'] ?? ''); ?>">
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Nationality</div>
                <div class="detail-value">
                    <input type="text" class="form-control" id="nationality" name="nationality" value="<?php echo htmlspecialchars($client['nationality'] ?? ''); ?>">
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Height (cm)</div>
                <div class="detail-value">
                    <input type="number" step="0.01" class="form-control" id="height" name="height" value="<?php echo htmlspecialchars($client['height'] ?? ''); ?>">
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Weight (kg)</div>
                <div class="detail-value">
                    <input type="number" step="0.01" class="form-control" id="weight" name="weight" value="<?php echo htmlspecialchars($client['weight'] ?? ''); ?>">
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Smoker</div>
                <div class="detail-value">
                    <select class="form-control" id="smoker" name="smoker">
                        <option value="">Select Option</option>
                        <option value="Yes" <?php echo ($client['smoker'] == 'Yes') ? 'selected' : ''; ?>>Yes</option>
                        <option value="No" <?php echo ($client['smoker'] == 'No') ? 'selected' : ''; ?>>No</option>
                    </select>
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Hospital Admission History</div>
                <div class="detail-value">
                    <textarea class="form-control" id="hospital_admission_history" name="hospital_admission_history" rows="3"><?php echo htmlspecialchars($client['hospital_admission_history'] ?? ''); ?></textarea>
                </div>
            </div>
        </div>
        
        <!-- Employment Information Section -->
        <div class="form-section">
            <div class="section-title">Employment Information</div>
            
            <div class="detail-row">
                <div class="detail-label">Occupation</div>
                <div class="detail-value">
                    <input type="text" class="form-control" id="occupation" name="occupation" value="<?php echo htmlspecialchars($client['occupation'] ?? ''); ?>">
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Exact Duties</div>
                <div class="detail-value">
                    <textarea class="form-control" id="exact_duties" name="exact_duties" rows="2"><?php echo htmlspecialchars($client['exact_duties'] ?? ''); ?></textarea>
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Nature of Business</div>
                <div class="detail-value">
                    <input type="text" class="form-control" id="nature_of_business" name="nature_of_business" value="<?php echo htmlspecialchars($client['nature_of_business'] ?? ''); ?>">
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Yearly Salary</div>
                <div class="detail-value">
                    <input type="number" step="0.01" class="form-control" id="salary_yearly" name="salary_yearly" value="<?php echo htmlspecialchars($client['salary_yearly'] ?? ''); ?>">
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Company Name</div>
                <div class="detail-value">
                    <input type="text" class="form-control" id="company_name" name="company_name" value="<?php echo htmlspecialchars($client['company_name'] ?? ''); ?>">
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Company Address</div>
                <div class="detail-value">
                    <textarea class="form-control" id="company_address" name="company_address" rows="2"><?php echo htmlspecialchars($client['company_address'] ?? ''); ?></textarea>
                </div>
            </div>
        </div>
        
        <div class="btn-container">
            <a href="client_view.php?id=<?php echo $clientId; ?>" class="btn btn-secondary">
                Cancel
            </a>
            <div>
                <a href="clients.php" class="btn btn-secondary">Back to List</a>
                <button type="submit" class="btn btn-primary">Update Client</button>
            </div>
        </div>
    </form>
</div>

<script>
    $(document).ready(function() {
        // Initialize Select2
        $('#agent_id').select2({
            placeholder: 'Select an agent',
            allowClear: true
        });
        
        $('#marital_status').select2({
            placeholder: 'Select marital status',
            allowClear: true
        });
        
        $('#gender').select2({
            placeholder: 'Select gender',
            allowClear: true
        });
        
        $('#smoker').select2({
            placeholder: 'Select option',
            allowClear: true
        });
    });
</script>

<?php include 'layout_footer.php'; ?> 