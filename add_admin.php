<?php
// add_admin.php - <PERSON><PERSON>t to add an admin user to the system

// Include the database configuration
require_once 'config.php';

// Admin details - CHANGE THESE TO YOUR PREFERRED VALUES
$username = 'admin';
$password = 'admin123'; // You should use a strong password in production
$email = '<EMAIL>';

// Check if the user already exists
$stmt = $pdo->prepare("SELECT * FROM users WHERE username = ? OR email = ?");
$stmt->execute([$username, $email]);
$existingUser = $stmt->fetch();

if ($existingUser) {
    echo "Error: A user with this username or email already exists.";
} else {
    // Hash the password
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
    
    // Insert the new admin user
    $stmt = $pdo->prepare("INSERT INTO users (username, email, password, created_at) VALUES (?, ?, ?, NOW())");
    $result = $stmt->execute([$username, $email, $hashedPassword]);
    
    if ($result) {
        $userId = $pdo->lastInsertId();
        echo "Success! Admin user created with ID: " . $userId;
        echo "<br><br>Username: " . $username;
        echo "<br>Email: " . $email;
        echo "<br>Password: " . $password . " (remember to change this after first login)";
        
        // Log the action
        logActivity($userId, 'user_created', 'Admin user created via setup script');
    } else {
        echo "Error: Failed to create admin user.";
    }
}
?>