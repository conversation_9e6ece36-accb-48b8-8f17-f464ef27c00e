<?php
// debug_search.php - A simple script to test the search functionality directly
session_start();
require_once 'config.php';

// Set content type to plain text for easy debugging
header('Content-Type: text/plain');

// Create a simple log function
function debug_log($message) {
    echo "LOG: " . $message . "\n";
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo "ERROR: Not logged in. Please log in first.\n";
    exit;
}

// Get query parameter
$query = isset($_GET['q']) ? trim($_GET['q']) : '';
echo "Query: " . $query . "\n\n";

if (empty($query)) {
    echo "ERROR: Empty query.\n";
    exit;
}

try {
    // Test database connection
    echo "Database connection check: ";
    if ($pdo) {
        echo "Connected\n";
    } else {
        echo "Failed\n";
        exit;
    }
    
    echo "Starting database queries...\n";
    
    // Search in clients table
    echo "\nCLIENTS SEARCH:\n";
    $clientSql = "SELECT 
        client_id as id,
        name,
        email,
        phone_number,
        'client' as type
    FROM clients 
    WHERE name LIKE :query 
    OR email LIKE :query 
    OR phone_number LIKE :query
    OR client_id LIKE :query
    LIMIT 5";
    
    echo "SQL: " . $clientSql . "\n";
    
    $clientStmt = $pdo->prepare($clientSql);
    $clientStmt->execute([':query' => "%$query%"]);
    $clients = $clientStmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Clients found: " . count($clients) . "\n";
    foreach ($clients as $i => $client) {
        echo ($i+1) . ". " . $client['name'] . " (" . $client['id'] . ") - " . $client['email'] . "\n";
    }
    
    // Search in agents table
    echo "\nAGENTS SEARCH:\n";
    $agentSql = "SELECT 
        agent_id as id,
        name,
        email,
        phone_number,
        'agent' as type
    FROM agents 
    WHERE name LIKE :query 
    OR email LIKE :query 
    OR phone_number LIKE :query
    OR agent_id LIKE :query
    LIMIT 5";
    
    echo "SQL: " . $agentSql . "\n";
    
    $agentStmt = $pdo->prepare($agentSql);
    $agentStmt->execute([':query' => "%$query%"]);
    $agents = $agentStmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Agents found: " . count($agents) . "\n";
    foreach ($agents as $i => $agent) {
        echo ($i+1) . ". " . $agent['name'] . " (" . $agent['id'] . ") - " . $agent['email'] . "\n";
    }
    
    // Combine results
    $results = array_merge($clients, $agents);
    
    // Sort results by name
    usort($results, function($a, $b) {
        return strcasecmp($a['name'], $b['name']);
    });
    
    $finalResults = array_slice($results, 0, 10); // Limit to 10 results total
    
    echo "\nCOMBINED RESULTS (sorted by name):\n";
    echo "Total results: " . count($finalResults) . "\n";
    foreach ($finalResults as $i => $result) {
        echo ($i+1) . ". " . $result['type'] . ": " . $result['name'] . " (" . $result['id'] . ") - " . $result['email'] . "\n";
    }
    
    echo "\nJSON OUTPUT:\n";
    echo json_encode($finalResults, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
} 