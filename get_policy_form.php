<?php
session_start();
require_once 'config.php';
require_once 'policies.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo "Unauthorized";
    exit;
}

// Get all agents for the dropdown
$agents = getAllAgents();

// Get the index parameter
$index = isset($_GET['index']) ? intval($_GET['index']) : 0;

// Return the HTML for a new policy form
echo pm_renderPolicyForm($index, [], false);
?> 