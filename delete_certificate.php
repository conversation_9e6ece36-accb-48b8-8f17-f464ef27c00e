<?php
session_start();
require_once 'config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Check if agent ID is provided
if (!isset($_GET['agent_id']) || empty($_GET['agent_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Invalid agent ID']);
    exit;
}

$agentId = $_GET['agent_id'];
$agent = getAgentById($agentId);

if (!$agent) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Agent not found']);
    exit;
}

// Delete the certificate file if it exists
if (!empty($agent['education_cert']) && file_exists($agent['education_cert'])) {
    if (unlink($agent['education_cert'])) {
        // Update the agent record to remove the certificate reference
        $data = $agent;
        $data['education_cert'] = null;
        
        if (updateAgent($agentId, $data)) {
            header('Content-Type: application/json');
            echo json_encode(['success' => true]);
            exit;
        }
    }
}

header('Content-Type: application/json');
echo json_encode(['success' => false, 'message' => 'Failed to delete certificate']);
exit; 