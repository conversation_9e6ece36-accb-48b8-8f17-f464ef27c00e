<?php
// login.php
session_start();

// Set cache control headers
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");
header("Expires: 0");

require_once 'config.php';

// If already logged in, redirect to dashboard
if (isset($_SESSION['user_id'])) {
    header('Location: dashboard.php');
    exit;
}

$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    if (empty($username) || empty($password)) {
        $error = 'Please enter both username and password';
    } else {
        $user = authenticateUser($username, $password);
        
        if ($user) {
            // Set session variables
            $_SESSION['user_id'] = $user['user_id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['email'] = $user['email'];
            $_SESSION['last_activity'] = time();
            
            // Log the login activity
            logActivity($user['user_id'], 'login', 'User logged in');
            
            // Redirect to dashboard
            header('Location: dashboard.php');
            exit;
        } else {
            $error = 'Invalid username or password';
        }
    }
}

// Get current year for copyright
$currentYear = date('Y');
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>Login - AmMetLife CRM</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #f0f4f8 0%, #d9e2ec 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            padding: 40px 20px;
        }
        .page-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            max-width: 1000px;
            width: 100%;
        }
        .login-wrapper {
            display: flex;
            width: 100%;
            max-width: 800px;
            margin: 20px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
        }
        .login-image {
            flex: 1;
            background: linear-gradient(rgba(0,0,0,0.5), rgba(0,0,0,0.5)), url('https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80');
            background-size: cover;
            background-position: center;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            padding: 30px;
            display: none; /* Hidden on mobile */
        }
        .login-image-content {
            text-align: center;
        }
        .login-image h2 {
            font-size: 28px;
            margin-bottom: 20px;
            font-weight: 300;
        }
        .login-image p {
            font-size: 16px;
            opacity: 0.9;
            line-height: 1.6;
        }
        .logo-container {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo-container img {
            width: 180px;
            height: auto;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
        }
        .login-form-container {
            flex: 1;
            background-color: white;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-control {
            height: 45px;
            border-radius: 5px;
            border: 1px solid #ddd;
            padding-left: 15px;
            transition: all 0.3s;
        }
        .form-control:focus {
            border-color: #e31b23;
            box-shadow: 0 0 0 0.2rem rgba(227, 27, 35, 0.25);
        }
        .btn-login {
            background-color: #e31b23;
            border-color: #e31b23;
            color: white;
            height: 45px;
            border-radius: 5px;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s;
        }
        .btn-login:hover {
            background-color: #c41920;
            border-color: #c41920;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(227, 27, 35, 0.3);
        }
        .login-title {
            text-align: center;
            font-weight: 600;
            color: #4a5568;
            margin-bottom: 30px;
            font-size: 24px;
        }
        .input-group-text {
            background-color: transparent;
            border-right: none;
        }
        .form-control.left-border {
            border-left: none;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            color: #4a5568;
            font-size: 14px;
            width: 100%;
            padding: 15px;
            border-top: 1px solid rgba(0,0,0,0.1);
        }
        .footer-links {
            margin-bottom: 10px;
        }
        .footer-links a {
            color: #4a5568;
            margin: 0 10px;
            text-decoration: none;
            transition: color 0.3s;
        }
        .footer-links a:hover {
            color: #e31b23;
        }
        .remember-me {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        .remember-me input {
            margin-right: 10px;
        }
        
        @media (min-width: 768px) {
            .login-image {
                display: flex;
            }
        }
    </style>
</head>
<body>
    <div class="page-container">
        <!-- Logo at the top -->
        <div class="logo-container">
            <img src="assets/logo.png" alt="AmMetLife Logo">
        </div>
        
        <div class="login-wrapper">
            <!-- Left side image (hidden on mobile) -->
            <div class="login-image">
                <div class="login-image-content">
                    <h2>Welcome to AmMetLife CRM</h2>
                    <p>Manage your clients and agents efficiently with our comprehensive customer relationship management system.</p>
                </div>
            </div>
            
            <!-- Right side login form -->
            <div class="login-form-container">
                <h3 class="login-title">Admin Login</h3>
                
                <?php if (!empty($error)): ?>
                    <div class="alert alert-danger" role="alert">
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>
                
                <form method="post" action="">
                    <div class="form-group">
                        <label for="username">Username</label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text"><i class="fas fa-user"></i></span>
                            </div>
                            <input type="text" class="form-control left-border" id="username" name="username" placeholder="Enter your username" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="password">Password</label>
                        <div class="input-group">
                            <div class="input-group-prepend">
                                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                            </div>
                            <input type="password" class="form-control left-border" id="password" name="password" placeholder="Enter your password" required>
                        </div>
                    </div>
                    
                    <div class="remember-me">
                        <input type="checkbox" id="remember" name="remember">
                        <label for="remember">Remember me</label>
                    </div>
                    
                    <button type="submit" class="btn btn-login">Login</button>
                </form>
            </div>
        </div>
        
        <!-- Footer with copyright and links -->
        <div class="footer">
            <div class="footer-links">
                <a href="#">Help</a>
                <a href="#">Privacy Policy</a>
                <a href="#">Terms of Service</a>
            </div>
            <div>
                &copy; <?php echo $currentYear; ?> AmMetLife CRM. All rights reserved.
            </div>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>