<?php
/**
 * live_search.php - Handles live search requests for the sidebar search
 * Returns search results as JSON for both agents and clients
 */
session_start();
require_once 'config.php';

// Set headers for AJAX requests
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// Debug log function
function debug_log($message) {
    // Comment this out in production
    error_log(print_r($message, true));
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['error' => 'Unauthorized', 'status' => 401]);
    exit;
}

// Get search query - minimum 2 characters required
$query = isset($_GET['query']) ? trim($_GET['query']) : '';
debug_log("Search query received: " . $query);

if (empty($query) || strlen($query) < 2) {
    echo json_encode([]);
    exit;
}

try {
    // Search in clients table
    $clientSql = "SELECT 
        client_id as id,
        name,
        email,
        phone_number,
        'client' as type
    FROM clients 
    WHERE name LIKE :query 
    OR email LIKE :query 
    OR phone_number LIKE :query
    OR client_id LIKE :query
    LIMIT 5";
    
    $clientStmt = $pdo->prepare($clientSql);
    $clientStmt->execute([':query' => "%$query%"]);
    $clients = $clientStmt->fetchAll(PDO::FETCH_ASSOC);
    debug_log("Clients found: " . count($clients));
    
    // Search in agents table
    $agentSql = "SELECT 
        agent_id as id,
        name,
        email,
        phone_number,
        'agent' as type
    FROM agents 
    WHERE name LIKE :query 
    OR email LIKE :query 
    OR phone_number LIKE :query
    OR agent_id LIKE :query
    LIMIT 5";
    
    $agentStmt = $pdo->prepare($agentSql);
    $agentStmt->execute([':query' => "%$query%"]);
    $agents = $agentStmt->fetchAll(PDO::FETCH_ASSOC);
    debug_log("Agents found: " . count($agents));
    
    // Combine results
    $results = array_merge($clients, $agents);
    
    // Sort results by name
    usort($results, function($a, $b) {
        return strcasecmp($a['name'], $b['name']);
    });
    
    $finalResults = array_slice($results, 0, 10); // Limit to 10 results total
    debug_log("Total results after filtering: " . count($finalResults));
    
    // Return results as JSON
    echo json_encode($finalResults);
    
} catch (Exception $e) {
    debug_log("Search error: " . $e->getMessage());
    debug_log("Exception trace: " . $e->getTraceAsString());
    echo json_encode([
        'error' => 'Search failed: ' . $e->getMessage(),
        'status' => 500
    ]);
} 