-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Apr 14, 2025 at 05:17 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `sistem_crm`
--

-- --------------------------------------------------------

--
-- Table structure for table `activity_logs`
--

CREATE TABLE `activity_logs` (
  `log_id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `action` varchar(100) NOT NULL,
  `details` text DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `timestamp` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `activity_logs`
--

INSERT INTO `activity_logs` (`log_id`, `user_id`, `action`, `details`, `ip_address`, `timestamp`) VALUES
(1, 1, 'user_created', 'Admin user created via setup script', '127.0.0.1', '2025-03-17 04:08:30'),
(2, 1, 'login', 'User logged in', '127.0.0.1', '2025-03-19 01:50:53'),
(3, 1, 'login', 'User logged in', '127.0.0.1', '2025-03-20 02:03:49'),
(4, 1, 'login', 'User logged in', '127.0.0.1', '2025-03-20 02:16:50'),
(5, 1, 'update_agent', 'Updated agent 123', '127.0.0.1', '2025-03-20 02:20:18'),
(6, 1, 'delete_agent', 'Agent ID: 123, Name: aeafea ejfh s', '127.0.0.1', '2025-03-20 02:21:42'),
(7, 1, 'update_agent', 'Updated agent. ID changed from AG001 to AG0011', '127.0.0.1', '2025-03-20 02:36:22'),
(8, 1, 'update_agent', 'Updated agent. ID changed from AG0011 to AG00111', '127.0.0.1', '2025-03-20 02:38:43'),
(9, 1, 'login', 'User logged in', '127.0.0.1', '2025-03-20 02:45:43'),
(10, 1, 'login', 'User logged in', '127.0.0.1', '2025-03-20 02:52:24'),
(11, 1, 'delete_agent', 'Agent ID: AG00111, Name: nasrullah', '127.0.0.1', '2025-03-20 02:57:54'),
(17, 1, 'login', 'User logged in', '127.0.0.1', '2025-03-28 02:01:04'),
(18, 1, 'login', 'User logged in', '127.0.0.1', '2025-04-07 04:01:03'),
(19, 1, 'login', 'User logged in', '127.0.0.1', '2025-04-07 06:39:17'),
(0, 1, 'login', 'User logged in', '::1', '2025-04-07 08:53:17'),
(0, 1, 'login', 'User logged in', '::1', '2025-04-08 02:11:27'),
(0, 1, 'login', 'User logged in', '::1', '2025-04-08 02:49:43'),
(0, 1, 'login', 'User logged in', '127.0.0.1', '2025-04-09 02:37:34'),
(0, 1, 'login', 'User logged in', '127.0.0.1', '2025-04-09 02:59:17'),
(0, 1, 'update_agent', 'Updated agent AG001556', '127.0.0.1', '2025-04-09 03:18:50'),
(0, 1, 'update_agent', 'Updated agent AG001556', '127.0.0.1', '2025-04-09 03:19:03'),
(0, 1, 'login', 'User logged in', '127.0.0.1', '2025-04-09 09:14:02');

-- --------------------------------------------------------

--
-- Table structure for table `agents`
--

CREATE TABLE `agents` (
  `agent_id` varchar(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `ic_number` varchar(20) NOT NULL,
  `gender` enum('Male','Female') NOT NULL,
  `date_of_birth` date NOT NULL,
  `phone_number` varchar(20) NOT NULL,
  `email` varchar(255) NOT NULL,
  `address` text NOT NULL,
  `beneficiary_phone` varchar(20) DEFAULT NULL,
  `work_experience` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `photo` varchar(255) DEFAULT NULL,
  `status` enum('Active','Pending','Inactive') NOT NULL DEFAULT 'Pending'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `agents`
--

INSERT INTO `agents` (`agent_id`, `name`, `ic_number`, `gender`, `date_of_birth`, `phone_number`, `email`, `address`, `beneficiary_phone`, `work_experience`, `created_at`, `updated_at`, `photo`, `status`) VALUES
('AG001556', 'Amiruddin', '000819060133', 'Male', '2025-04-11', '0109218919', '<EMAIL>', 'Kempadang 12', '', '', '2025-04-09 03:08:17', '2025-04-09 03:19:03', NULL, 'Pending');

-- --------------------------------------------------------

--
-- Table structure for table `agent_documents`
--

CREATE TABLE `agent_documents` (
  `document_id` int(11) NOT NULL,
  `agent_id` varchar(11) NOT NULL,
  `document_type_id` int(11) NOT NULL,
  `file_name` varchar(255) NOT NULL,
  `file_path` varchar(255) NOT NULL,
  `file_size` int(11) DEFAULT NULL,
  `upload_date` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `agent_documents`
--

INSERT INTO `agent_documents` (`document_id`, `agent_id`, `document_type_id`, `file_name`, `file_path`, `file_size`, `upload_date`) VALUES
(4, 'AG001', 1, 'Itinerary_Program_Tabled.pdf', 'uploads/documents/AG001_education_cert_1742439696_Itinerary_Program_Tabled.pdf', 99480, '2025-03-20 11:01:36');

-- --------------------------------------------------------

--
-- Table structure for table `beneficiaries`
--

CREATE TABLE `beneficiaries` (
  `beneficiary_id` int(11) NOT NULL,
  `policy_id` varchar(50) NOT NULL,
  `name` varchar(255) NOT NULL,
  `ic_number` varchar(20) NOT NULL,
  `relationship` varchar(50) NOT NULL,
  `date_of_birth` date NOT NULL,
  `phone_number` varchar(20) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `percentage` decimal(5,2) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `beneficiaries`
--

INSERT INTO `beneficiaries` (`beneficiary_id`, `policy_id`, `name`, `ic_number`, `relationship`, `date_of_birth`, `phone_number`, `email`, `address`, `percentage`, `created_at`, `updated_at`) VALUES
(1, '4567', 'ag', '4576', 'faewtg', '2025-04-09', '567', '<EMAIL>', 'aerg', 10.00, '2025-04-14 03:01:20', '2025-04-14 03:01:20');

-- --------------------------------------------------------

--
-- Table structure for table `clients`
--

CREATE TABLE `clients` (
  `client_id` varchar(20) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `name` varchar(255) NOT NULL,
  `ic_number` varchar(20) NOT NULL,
  `client_number` varchar(50) NOT NULL,
  `gender` enum('Male','Female','Other') NOT NULL,
  `date_of_birth` date NOT NULL,
  `phone_number` varchar(20) NOT NULL,
  `email` varchar(255) NOT NULL,
  `address` text NOT NULL,
  `marital_status` varchar(50) DEFAULT NULL,
  `race` varchar(50) DEFAULT NULL,
  `religion` varchar(50) DEFAULT NULL,
  `nationality` varchar(50) DEFAULT NULL,
  `occupation` varchar(255) DEFAULT NULL,
  `exact_duties` text DEFAULT NULL,
  `nature_of_business` text DEFAULT NULL,
  `salary_yearly` decimal(10,2) DEFAULT NULL,
  `company_name` varchar(255) DEFAULT NULL,
  `company_address` text DEFAULT NULL,
  `weight` decimal(5,2) DEFAULT NULL,
  `height` decimal(5,2) DEFAULT NULL,
  `smoker` enum('Smoker','Non-smoker') DEFAULT NULL,
  `hospital_admission_history` text DEFAULT NULL,
  `status` enum('Pending','Approved','Rejected') NOT NULL DEFAULT 'Pending',
  `policy_id` varchar(50) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `clients`
--

INSERT INTO `clients` (`client_id`, `created_at`, `updated_at`, `name`, `ic_number`, `client_number`, `gender`, `date_of_birth`, `phone_number`, `email`, `address`, `marital_status`, `race`, `religion`, `nationality`, `occupation`, `exact_duties`, `nature_of_business`, `salary_yearly`, `company_name`, `company_address`, `weight`, `height`, `smoker`, `hospital_admission_history`, `status`, `policy_id`) VALUES
('PENDING_1744181904_7', '2025-04-09 06:58:24', '2025-04-09 06:58:24', 'AM', '0001', '', 'Male', '2025-04-16', '123', '<EMAIL>', 'aef', '', '', '', '', '', '', '', 0.00, '', '', 0.00, 0.00, '', '', 'Pending', NULL),
('PENDING_1744242297_2', '2025-04-09 23:44:57', '2025-04-09 23:44:57', 'faef', '13123', '', 'Male', '2025-04-10', '123123', '<EMAIL>', 'aefsef', '', '', '', '', '', '', '', 0.00, '', '', 0.00, 0.00, '', '', 'Pending', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `document_types`
--

CREATE TABLE `document_types` (
  `document_type_id` int(11) NOT NULL,
  `type_name` varchar(50) NOT NULL,
  `description` text DEFAULT NULL,
  `required` tinyint(1) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `document_types`
--

INSERT INTO `document_types` (`document_type_id`, `type_name`, `description`, `required`) VALUES
(1, 'Education Certificate', 'Education certificates and qualifications', 1),
(2, 'NRIC', 'National Registration Identity Card', 1),
(3, 'Beneficiary NRIC', 'Beneficiary National Registration Identity Card', 0),
(4, 'Passport', 'Passport photo or document', 0),
(5, 'Other Document', 'Other supporting documents', 0);

-- --------------------------------------------------------

--
-- Table structure for table `educationdetails`
--

CREATE TABLE `educationdetails` (
  `id` int(11) NOT NULL,
  `agent_id` varchar(11) DEFAULT NULL,
  `level` varchar(50) DEFAULT NULL,
  `year_completed` year(4) DEFAULT NULL,
  `institution_name` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `policies`
--

CREATE TABLE `policies` (
  `policy_id` varchar(50) NOT NULL,
  `client_id` varchar(20) NOT NULL,
  `agent_id` varchar(11) NOT NULL,
  `plan_type` varchar(100) NOT NULL,
  `basic_plan_rider` text DEFAULT NULL,
  `sum_covered` decimal(10,2) NOT NULL,
  `coverage_term` varchar(50) NOT NULL,
  `contribution` decimal(10,2) NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `status` enum('Active','Inactive','Pending','Expired','Cancelled') NOT NULL DEFAULT 'Pending',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `policies`
--

INSERT INTO `policies` (`policy_id`, `client_id`, `agent_id`, `plan_type`, `basic_plan_rider`, `sum_covered`, `coverage_term`, `contribution`, `start_date`, `end_date`, `status`, `created_at`, `updated_at`) VALUES
('123', 'PENDING_1744181904_7', 'AG001556', 'aef', 'amfo', 123.00, '1231', 1233.00, '2025-04-09', '2026-04-09', 'Pending', '2025-04-09 06:58:24', '2025-04-09 06:58:24'),
('4567', 'PENDING_1744242297_2', 'AG001556', 'fjygj', 'dth', 2345.00, '5235', 24.00, '2025-04-14', '2026-04-14', 'Pending', '2025-04-14 03:01:20', '2025-04-14 03:01:20'),
('56787', 'PENDING_1744242297_2', 'AG001556', '865 ', 'resag', 235.00, '346', 345.00, '2025-04-14', '2026-04-14', 'Pending', '2025-04-14 02:48:50', '2025-04-14 02:48:50'),
('8678', 'PENDING_1744242297_2', 'AG001556', 'dtfj', 'aef', 345.00, '346', 346.00, '2025-04-14', '2026-04-14', 'Pending', '2025-04-14 02:54:05', '2025-04-14 02:54:05');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `user_id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `email` varchar(100) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`user_id`, `username`, `password`, `email`, `created_at`) VALUES
(1, 'admin', '$2y$10$DMoCmFtHeqecF6eY3BnWVu7QEeklUjoC1Xlwcor4Njx5SmcJywZNe', '<EMAIL>', '2025-03-17 04:08:30');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `agents`
--
ALTER TABLE `agents`
  ADD PRIMARY KEY (`agent_id`);

--
-- Indexes for table `beneficiaries`
--
ALTER TABLE `beneficiaries`
  ADD PRIMARY KEY (`beneficiary_id`),
  ADD KEY `policy_id` (`policy_id`);

--
-- Indexes for table `clients`
--
ALTER TABLE `clients`
  ADD PRIMARY KEY (`client_id`),
  ADD KEY `clients_ibfk_1` (`policy_id`);

--
-- Indexes for table `policies`
--
ALTER TABLE `policies`
  ADD PRIMARY KEY (`policy_id`),
  ADD KEY `client_id` (`client_id`),
  ADD KEY `agent_id` (`agent_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `beneficiaries`
--
ALTER TABLE `beneficiaries`
  MODIFY `beneficiary_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `beneficiaries`
--
ALTER TABLE `beneficiaries`
  ADD CONSTRAINT `beneficiaries_ibfk_1` FOREIGN KEY (`policy_id`) REFERENCES `policies` (`policy_id`) ON DELETE CASCADE;

--
-- Constraints for table `clients`
--
ALTER TABLE `clients`
  ADD CONSTRAINT `clients_ibfk_1` FOREIGN KEY (`policy_id`) REFERENCES `policies` (`policy_id`) ON DELETE SET NULL;

--
-- Constraints for table `policies`
--
ALTER TABLE `policies`
  ADD CONSTRAINT `policies_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`client_id`) ON DELETE CASCADE,
  ADD CONSTRAINT `policies_ibfk_2` FOREIGN KEY (`agent_id`) REFERENCES `agents` (`agent_id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
