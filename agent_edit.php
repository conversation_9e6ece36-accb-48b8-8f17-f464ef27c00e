<?php
session_start();
require_once 'config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Check if agent ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header('Location: agents.php?error=Invalid agent ID');
    exit;
}

$agentId = $_GET['id'];
$agent = getAgentById($agentId);

if (!$agent) {
    header('Location: agents.php?error=Agent not found');
    exit;
}

// Add this BEFORE the <CURRENT_CURSOR_POSITION> tag to check database connection
try {
    $pdo->query("SELECT 1");
    // If we get here, connection is working
} catch (PDOException $e) {
    die("Database connection error: " . $e->getMessage());
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Add this for debugging
    error_log("POST Data: " . print_r($_POST, true));
    error_log("FILES Data: " . print_r($_FILES, true));
    
    try {
        // Check if agent ID has changed and validate
        $newAgentId = trim($_POST['agent_id'] ?? '');
        
        // Validate agent ID format and length
        if (empty($newAgentId)) {
            $error = "Agent ID cannot be empty";
        } elseif (!preg_match('/^[A-Za-z0-9]+$/', $newAgentId)) {
            $error = "Agent ID can only contain letters and numbers";
        } elseif (strlen($newAgentId) > 20) {
            $error = "Agent ID cannot be longer than 20 characters";
        } 
        // Verify the new agent ID doesn't exist (unless it's the same one)
        elseif ($newAgentId !== $agentId && agentIdExists($newAgentId)) {
            $error = "Agent ID '{$newAgentId}' already exists";
        } else {
            $data = [
                'agent_id' => $newAgentId,
                'name' => $_POST['name'],
                'ic_number' => $_POST['ic_number'],
                'gender' => $_POST['gender'],
                'date_of_birth' => $_POST['date_of_birth'],
                'phone_number' => $_POST['phone_number'],
                'email' => $_POST['email'],
                'address' => $_POST['address'],
                'beneficiary_phone' => $_POST['beneficiary_phone'],
                'work_experience' => $_POST['work_experience']
            ];

            // Handle photo upload
            if (isset($_FILES['photo']) && $_FILES['photo']['error'] === UPLOAD_ERR_OK) {
                $uploadDir = 'uploads/photos/';
                if (!file_exists($uploadDir)) {
                    mkdir($uploadDir, 0777, true);
                }
                
                // Validate file type
                $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
                $fileType = $_FILES['photo']['type'];
                
                if (!in_array($fileType, $allowedTypes)) {
                    throw new Exception('Invalid file type. Only JPG, PNG and GIF are allowed.');
                }
                
                $fileExtension = strtolower(pathinfo($_FILES['photo']['name'], PATHINFO_EXTENSION));
                $newFileName = 'photo_' . $agentId . '_' . time() . '.' . $fileExtension;
                $targetPath = $uploadDir . $newFileName;
                
                // Delete old photo if exists
                if (!empty($agent['photo']) && file_exists($agent['photo'])) {
                    unlink($agent['photo']);
                }
                
                if (move_uploaded_file($_FILES['photo']['tmp_name'], $targetPath)) {
                    $data['photo'] = $targetPath;
                } else {
                    throw new Exception('Failed to upload photo.');
                }
            }

            // Handle document uploads
            $documentTypes = [
                'education_cert' => 1,  // document_type_id from document_types table
                'nric_copy' => 2,
                'beneficiary_nric' => 3
            ];

            foreach ($documentTypes as $docType => $typeId) {
                if (isset($_FILES[$docType]) && $_FILES[$docType]['error'] === UPLOAD_ERR_OK) {
                    $uploadDir = 'uploads/documents/';
                    if (!file_exists($uploadDir)) {
                        mkdir($uploadDir, 0777, true);
                    }
                    
                    // Validate file type
                    $allowedTypes = ['application/pdf'];
                    $fileType = $_FILES[$docType]['type'];
                    
                    if (!in_array($fileType, $allowedTypes)) {
                        throw new Exception('Invalid file type for ' . $docType . '. Only PDF files are allowed.');
                    }
                    
                    $fileExtension = strtolower(pathinfo($_FILES[$docType]['name'], PATHINFO_EXTENSION));
                    $newFileName = $docType . '_' . $agentId . '_' . time() . '.' . $fileExtension;
                    $targetPath = $uploadDir . $newFileName;
                    
                    if (move_uploaded_file($_FILES[$docType]['tmp_name'], $targetPath)) {
                        // Delete existing document if any
                        $stmt = $pdo->prepare("DELETE FROM agent_documents WHERE agent_id = ? AND document_type_id = ?");
                        $stmt->execute([$agentId, $typeId]);
                        
                        // Insert new document record
                        $stmt = $pdo->prepare("INSERT INTO agent_documents (agent_id, document_type_id, file_name, file_path, file_size) VALUES (?, ?, ?, ?, ?)");
                        $stmt->execute([
                            $agentId,
                            $typeId,
                            $_FILES[$docType]['name'],
                            $targetPath,
                            $_FILES[$docType]['size']
                        ]);
                    } else {
                        throw new Exception('Failed to upload ' . $docType);
                    }
                }
            }

            // Add this debug line before updateAgent call
            error_log("Updating agent: oldId=" . $agentId . ", newData=" . print_r($data, true));
            
            // Update agent details with error handling
            $success = updateAgent($agentId, $data);
            if ($success) {
                // Use the new agent ID in the redirect URL if it was changed
                $redirectId = ($newAgentId !== $agentId) ? $newAgentId : $agentId;
                header('Location: agent_view.php?id=' . urlencode($redirectId) . '&success=Agent updated successfully');
                exit;
            } else {
                // Add more detailed error info
                error_log("Failed to update agent: " . json_encode(error_get_last()));
                throw new Exception('Failed to update agent details in database');
            }
        }

        // If there was an error, display it to the user
        if (isset($error)) {
            $_SESSION['error'] = $error;
        }
    } catch (Exception $e) {
        $_SESSION['error'] = $e->getMessage();
        // Add error logging
        error_log("Exception during agent update: " . $e->getMessage());
    }
}

// Just use the function that's already declared in config.php
$agentDocuments = getAgentDocuments($agentId);

$pageTitle = 'EDIT AGENT';
include 'layout.php';
?>

<style>
.detail-container {
    max-width: 1000px;
    margin-left: 400px;
    margin-bottom: 50px;
    padding: 30px;
    background: #fff;
    box-shadow: 0 0 20px rgba(0,0,0,0.05);
    border-radius: 15px;
}

.agent-header {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 40px;
    text-align: center;
    color: #2c3e50;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.agent-info {
    display: flex;
    gap: 40px;
    margin-bottom: 40px;
    background: #f8f9fa;
    padding: 30px;
    border-radius: 12px;
}

.agent-photo {
    width: 150px;
    height: 200px;
    background-color: #e9ecef;
    flex-shrink: 0;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.agent-photo i {
    font-size: 48px;
    color: #adb5bd;
    margin-bottom: 10px;
}

.photo-upload-btn {
    font-size: 12px;
    padding: 6px 12px;
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 10px;
    transition: all 0.3s ease;
}

.photo-upload-btn:hover {
    background: #e9ecef;
}

.agent-details {
    flex-grow: 1;
}

.detail-row {
    margin-bottom: 15px;
    display: flex;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    width: 180px;
    font-weight: 600;
    color: #495057;
    padding-top: 8px;
}

.detail-value {
    flex-grow: 1;
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

textarea.form-control {
    min-height: 100px;
    resize: vertical;
}

.section-container {
    margin-top: 40px;
    background: #f8f9fa;
    padding: 25px;
    border-radius: 12px;
}

.section-title {
    font-weight: 600;
    font-size: 18px;
    color: #2c3e50;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e9ecef;
}

.document-section {
    margin-top: 40px;
    background: #f8f9fa;
    padding: 25px;
    border-radius: 12px;
}

.document-upload {
    display: flex;
    align-items: center;
    padding: 15px;
    margin-bottom: 15px;
    background: white;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.document-upload .doc-icon {
    margin-right: 15px;
    color: #e74c3c;
    font-size: 20px;
}

.document-upload .doc-info {
    flex-grow: 1;
}

.document-upload .doc-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
}

.document-upload .doc-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.doc-actions .btn-view {
    padding: 6px 12px;
    background: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.doc-actions .btn-delete {
    padding: 6px 12px;
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.doc-actions .btn-upload {
    padding: 6px 12px;
    background: #2ecc71;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.doc-actions button:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.doc-actions .file-input {
    display: none;
}

.btn-container {
    margin-top: 40px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.btn {
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #2c3e50;
    border: none;
    color: white;
}

.btn-primary:hover {
    background-color: #1e2a37;
    transform: translateY(-2px);
}

.btn-secondary {
    background-color: #6c757d;
    border: none;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
    transform: translateY(-2px);
}
</style>

<div class="detail-container">
    <div class="agent-header">
        EDIT AGENT
    </div>

    <form method="POST" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF'] . '?id=' . $agentId); ?>" enctype="multipart/form-data">
        <div class="agent-info">
            <div class="agent-photo">
                <?php if (!empty($agent['photo']) && file_exists($agent['photo'])): ?>
                    <img src="<?php echo htmlspecialchars($agent['photo']); ?>" alt="Agent Photo" style="width: 100%; height: 100%; object-fit: cover; border-radius: 8px;">
                <?php else: ?>
                    <i class="fas fa-user"></i>
                <?php endif; ?>
                <input type="file" id="photo" name="photo" class="d-none" accept="image/*" onchange="previewImage(this)">
                <label for="photo" class="photo-upload-btn">Change Photo</label>
            </div>

            <div class="agent-details">
                <?php if (isset($_SESSION['error'])): ?>
                    <div class="alert alert-danger">
                        <?php 
                            echo htmlspecialchars($_SESSION['error']); 
                            unset($_SESSION['error']); 
                        ?>
                    </div>
                <?php endif; ?>
                <div class="detail-row">
                    <div class="detail-label">Name :</div>
                    <div class="detail-value">
                        <input type="text" class="form-control" name="name" value="<?php echo htmlspecialchars($agent['name']); ?>" required>
                    </div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">IC Number :</div>
                    <div class="detail-value">
                        <input type="text" class="form-control" name="ic_number" value="<?php echo htmlspecialchars($agent['ic_number']); ?>" required>
                    </div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Agent ID :</div>
                    <div class="detail-value">
                        <input type="text" class="form-control" name="agent_id" value="<?php echo htmlspecialchars($agent['agent_id']); ?>">
                    </div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Gender :</div>
                    <div class="detail-value">
                        <select class="form-control" name="gender" required id="gender-select" oninvalid="this.setCustomValidity('Please select the gender.')" onchange="this.setCustomValidity('')">
                            <option value="">Please select the gender.</option>
                            <option value="Male" <?php echo $agent['gender'] === 'Male' ? 'selected' : ''; ?>>Male</option>
                            <option value="Female" <?php echo $agent['gender'] === 'Female' ? 'selected' : ''; ?>>Female</option>
                        </select>
                    </div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Date Of Birth :</div>
                    <div class="detail-value">
                        <input type="date" class="form-control" name="date_of_birth" value="<?php echo htmlspecialchars($agent['date_of_birth']); ?>" required>
                    </div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Phone Number :</div>
                    <div class="detail-value">
                        <input type="tel" class="form-control strict-numbers-only" name="phone_number" value="<?php echo htmlspecialchars($agent['phone_number']); ?>" required pattern="[0-9]*" inputmode="numeric" onkeypress="return event.charCode >= 48 && event.charCode <= 57" maxlength="15">
                    </div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Email :</div>
                    <div class="detail-value">
                        <input type="email" class="form-control" name="email" value="<?php echo htmlspecialchars($agent['email']); ?>" required>
                    </div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Address :</div>
                    <div class="detail-value">
                        <textarea class="form-control" name="address" required><?php echo htmlspecialchars($agent['address']); ?></textarea>
                    </div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Beneficiary's Phone :</div>
                    <div class="detail-value">
                        <input type="tel" class="form-control strict-numbers-only" name="beneficiary_phone" value="<?php echo htmlspecialchars($agent['beneficiary_phone']); ?>" pattern="[0-9]*" inputmode="numeric" onkeypress="return event.charCode >= 48 && event.charCode <= 57" maxlength="15">
                    </div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Work Experience :</div>
                    <div class="detail-value">
                        <textarea class="form-control" name="work_experience"><?php echo htmlspecialchars($agent['work_experience']); ?></textarea>
                    </div>
                </div>
            </div>
        </div>

        <div class="document-section">
            <div class="section-title">Documents</div>
            
            <!-- Education Certificate -->
            <div class="document-upload">
                <i class="fas fa-file-pdf doc-icon"></i>
                <div class="doc-info">
                    <div class="doc-title">Education Certificate</div>
                    <?php if (isset($agentDocuments['Education Certificate'])): ?>
                        <div class="doc-filename"><?php echo htmlspecialchars($agentDocuments['Education Certificate']['file_name']); ?></div>
                    <?php endif; ?>
                </div>
                <div class="doc-actions">
                    <?php if (isset($agentDocuments['Education Certificate'])): ?>
                        <a href="<?php echo htmlspecialchars($agentDocuments['Education Certificate']['file_path']); ?>" class="btn-view" target="_blank">
                            <i class="fas fa-eye"></i> View
                        </a>
                        <button type="button" class="btn-delete" onclick="deleteDocument('education_cert', <?php echo $agentDocuments['Education Certificate']['document_id']; ?>)">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    <?php endif; ?>
                    <label class="btn-upload">
                        <i class="fas fa-upload"></i> Upload New
                        <input type="file" name="education_cert" class="file-input" accept=".pdf" onchange="updateFileName(this)">
                    </label>
                </div>
            </div>

            <!-- NRIC Copy -->
            <div class="document-upload">
                <i class="fas fa-file-pdf doc-icon"></i>
                <div class="doc-info">
                    <div class="doc-title">Photocopy of NRIC</div>
                    <?php if (isset($agentDocuments['Photocopy of NRIC'])): ?>
                        <div class="doc-filename"><?php echo htmlspecialchars($agentDocuments['Photocopy of NRIC']['file_name']); ?></div>
                    <?php endif; ?>
                </div>
                <div class="doc-actions">
                    <?php if (isset($agentDocuments['Photocopy of NRIC'])): ?>
                        <a href="<?php echo htmlspecialchars($agentDocuments['Photocopy of NRIC']['file_path']); ?>" class="btn-view" target="_blank">
                            <i class="fas fa-eye"></i> View
                        </a>
                        <button type="button" class="btn-delete" onclick="deleteDocument('nric_copy', <?php echo $agentDocuments['Photocopy of NRIC']['document_id']; ?>)">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    <?php endif; ?>
                    <label class="btn-upload">
                        <i class="fas fa-upload"></i> Upload New
                        <input type="file" name="nric_copy" class="file-input" accept=".pdf" onchange="updateFileName(this)">
                    </label>
                </div>
            </div>

            <!-- Beneficiary's NRIC -->
            <div class="document-upload">
                <i class="fas fa-file-pdf doc-icon"></i>
                <div class="doc-info">
                    <div class="doc-title">Photocopy of Beneficiary's NRIC</div>
                    <?php if (isset($agentDocuments['Photocopy of Beneficiary\'s NRIC'])): ?>
                        <div class="doc-filename"><?php echo htmlspecialchars($agentDocuments['Photocopy of Beneficiary\'s NRIC']['file_name']); ?></div>
                    <?php endif; ?>
                </div>
                <div class="doc-actions">
                    <?php if (isset($agentDocuments['Photocopy of Beneficiary\'s NRIC'])): ?>
                        <a href="<?php echo htmlspecialchars($agentDocuments['Photocopy of Beneficiary\'s NRIC']['file_path']); ?>" class="btn-view" target="_blank">
                            <i class="fas fa-eye"></i> View
                        </a>
                        <button type="button" class="btn-delete" onclick="deleteDocument('beneficiary_nric', <?php echo $agentDocuments['Photocopy of Beneficiary\'s NRIC']['document_id']; ?>)">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    <?php endif; ?>
                    <label class="btn-upload">
                        <i class="fas fa-upload"></i> Upload New
                        <input type="file" name="beneficiary_nric" class="file-input" accept=".pdf" onchange="updateFileName(this)">
                    </label>
                </div>
            </div>
        </div>

        <div class="btn-container">
            <a href="agent_view.php?id=<?php echo $agentId; ?>" class="btn btn-secondary">
                Cancel
            </a>
            <button type="submit" class="btn btn-primary">
                Save Changes
            </button>
        </div>
    </form>
</div>

<script>
function deleteDocument(docType, documentId) {
    if (confirm('Are you sure you want to delete this document?')) {
        fetch('delete_document.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                document_id: documentId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Failed to delete document: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while deleting the document');
        });
    }
}

function updateFileName(input) {
    const fileName = input.files[0]?.name;
    if (fileName) {
        const docInfo = input.closest('.document-upload').querySelector('.doc-info');
        let fileNameDiv = docInfo.querySelector('.doc-filename');
        if (!fileNameDiv) {
            fileNameDiv = document.createElement('div');
            fileNameDiv.className = 'doc-filename';
            docInfo.appendChild(fileNameDiv);
        }
        fileNameDiv.textContent = 'Selected: ' + fileName;
    }
}

function previewImage(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        const photoContainer = input.closest('.agent-photo');
        
        reader.onload = function(e) {
            // Remove existing icon if present
            const existingIcon = photoContainer.querySelector('i');
            if (existingIcon) {
                existingIcon.remove();
            }
            
            // Remove existing image if present
            const existingImg = photoContainer.querySelector('img');
            if (existingImg) {
                existingImg.remove();
            }
            
            // Create and add new image
            const img = document.createElement('img');
            img.src = e.target.result;
            img.style.width = '100%';
            img.style.height = '100%';
            img.style.objectFit = 'cover';
            img.style.borderRadius = '8px';
            
            // Insert the image before the input element
            photoContainer.insertBefore(img, input);
        };
        
        reader.readAsDataURL(input.files[0]);
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Set custom validation message for gender select
    const genderSelect = document.getElementById('gender-select');
    genderSelect.addEventListener('invalid', function(e) {
        if (genderSelect.validity.valueMissing) {
            genderSelect.setCustomValidity('Please select the gender.');
        }
    });
    
    genderSelect.addEventListener('change', function() {
        genderSelect.setCustomValidity('');
    });
    
    // STRICT NUMBERS-ONLY VALIDATION - MULTIPLE LAYERS OF PROTECTION
    document.querySelectorAll('.strict-numbers-only').forEach(function(input) {
        // Layer 1: Clear any non-numeric characters on load
        input.value = input.value.replace(/[^0-9]/g, '');
        
        // Layer 2: Input event listener to immediately remove non-numeric chars
        input.addEventListener('input', function(e) {
            const numericValue = this.value.replace(/[^0-9]/g, '');
            if (this.value !== numericValue) {
                this.value = numericValue;
            }
        });
        
        // Layer 3: Keydown event to prevent entering non-numeric chars
        input.addEventListener('keydown', function(e) {
            // Allow: backspace, delete, tab, escape, enter, navigation
            if ([46, 8, 9, 27, 13, 37, 38, 39, 40].indexOf(e.keyCode) !== -1 ||
                // Allow: Ctrl+A, Ctrl+C, Ctrl+X, Ctrl+V
                (e.keyCode === 65 && e.ctrlKey === true) ||
                (e.keyCode === 67 && e.ctrlKey === true) ||
                (e.keyCode === 88 && e.ctrlKey === true) ||
                // Allow: home, end
                (e.keyCode >= 35 && e.keyCode <= 39)) {
                // let it happen, don't do anything
                return;
            }
            
            // Ensure that it's a number and stop the keypress if not
            if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) && 
                (e.keyCode < 96 || e.keyCode > 105)) {
                e.preventDefault();
            }
        });
        
        // Layer 4: Paste event to filter and clean pasted content
        input.addEventListener('paste', function(e) {
            e.preventDefault();
            const pastedText = (e.clipboardData || window.clipboardData).getData('text');
            this.value = this.value + pastedText.replace(/[^0-9]/g, '');
        });
        
        // Layer 5: Focus event to validate content when field gets focus
        input.addEventListener('focus', function() {
            this.value = this.value.replace(/[^0-9]/g, '');
        });
        
        // Layer 6: Blur event to validate when leaving the field
        input.addEventListener('blur', function() {
            this.value = this.value.replace(/[^0-9]/g, '');
        });
    });
});
</script>

<?php include 'layout_footer.php'; ?> 