<?php
/**
 * Policies Management Functions
 * This file contains functions for managing insurance policies
 */

// Only define functions if they don't already exist
if (!function_exists('pm_getClientPolicies')) {
    // Function to get all policies for a client
    function pm_getClientPolicies($client_id) {
        global $pdo;
        
        try {
            $stmt = $pdo->prepare("SELECT p.*, a.name as agent_name 
                                  FROM policies p 
                                  LEFT JOIN agents a ON p.agent_id = a.agent_id 
                                  WHERE p.client_id = ?
                                  ORDER BY p.created_at DESC");
            $stmt->execute([$client_id]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error fetching client policies: " . $e->getMessage());
            return [];
        }
    }
}

if (!function_exists('pm_getPolicyById')) {
    // Function to get a single policy by ID
    function pm_getPolicyById($policy_id) {
        global $pdo;
        
        try {
            $stmt = $pdo->prepare("SELECT p.*, a.name as agent_name 
                                  FROM policies p 
                                  LEFT JOIN agents a ON p.agent_id = a.agent_id 
                                  WHERE p.id = ?");
            $stmt->execute([$policy_id]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error fetching policy: " . $e->getMessage());
            return null;
        }
    }
}

if (!function_exists('pm_addPolicy')) {
    // Function to add a new policy
    function pm_addPolicy($policy_data) {
        global $pdo;
        
        try {
            $sql = "INSERT INTO policies (
                client_id, plan_type, policy_id, basic_plan_rider,
                sum_covered, coverage_term, contribution, agent_id, 
                status, start_date, end_date
            ) VALUES (
                :client_id, :plan_type, :policy_id, :basic_plan_rider,
                :sum_covered, :coverage_term, :contribution, :agent_id, 
                :status, :start_date, :end_date
            )";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute([
                ':client_id' => $policy_data['client_id'],
                ':plan_type' => $policy_data['plan_type'],
                ':policy_id' => $policy_data['policy_id'],
                ':basic_plan_rider' => $policy_data['basic_plan_rider'],
                ':sum_covered' => $policy_data['sum_covered'],
                ':coverage_term' => $policy_data['coverage_term'],
                ':contribution' => $policy_data['contribution'],
                ':agent_id' => $policy_data['agent_id'],
                ':status' => $policy_data['status'] ?? 'Pending',
                ':start_date' => $policy_data['start_date'] ?? date('Y-m-d'),
                ':end_date' => $policy_data['end_date'] ?? date('Y-m-d', strtotime('+1 year'))
            ]);
            
            return $pdo->lastInsertId();
        } catch (Exception $e) {
            error_log("Error adding policy: " . $e->getMessage());
            return false;
        }
    }
}

if (!function_exists('pm_updatePolicy')) {
    // Function to update an existing policy
    function pm_updatePolicy($policy_id, $policy_data) {
        global $pdo;
        
        try {
            $sql = "UPDATE policies SET
                plan_type = :plan_type,
                policy_id = :policy_id,
                basic_plan_rider = :basic_plan_rider,
                sum_covered = :sum_covered,
                coverage_term = :coverage_term,
                contribution = :contribution,
                agent_id = :agent_id,
                status = :status,
                start_date = :start_date,
                end_date = :end_date,
                updated_at = NOW()
                WHERE id = :id";
            
            $stmt = $pdo->prepare($sql);
            $stmt->execute([
                ':id' => $policy_id,
                ':plan_type' => $policy_data['plan_type'],
                ':policy_id' => $policy_data['policy_id'],
                ':basic_plan_rider' => $policy_data['basic_plan_rider'],
                ':sum_covered' => $policy_data['sum_covered'],
                ':coverage_term' => $policy_data['coverage_term'],
                ':contribution' => $policy_data['contribution'],
                ':agent_id' => $policy_data['agent_id'],
                ':status' => $policy_data['status'] ?? 'Pending',
                ':start_date' => $policy_data['start_date'] ?? date('Y-m-d'),
                ':end_date' => $policy_data['end_date'] ?? date('Y-m-d', strtotime('+1 year'))
            ]);
            
            return true;
        } catch (Exception $e) {
            error_log("Error updating policy: " . $e->getMessage());
            return false;
        }
    }
}

if (!function_exists('pm_deletePolicy')) {
    // Function to delete a policy
    function pm_deletePolicy($policy_id) {
        global $pdo;
        
        try {
            $stmt = $pdo->prepare("DELETE FROM policies WHERE id = ?");
            $stmt->execute([$policy_id]);
            return true;
        } catch (Exception $e) {
            error_log("Error deleting policy: " . $e->getMessage());
            return false;
        }
    }
}

if (!function_exists('pm_renderPolicyForm')) {
    // Function to render a policy form (for adding new policies)
    function pm_renderPolicyForm($index = 0, $data = [], $is_first = true) {
        global $agents;
        
        // Generate unique ID for this form
        $form_id = "policy_form_" . $index;
        
        // Default values
        $agent_id = $data['agent_id'] ?? '';
        $plan_type = $data['plan_type'] ?? '';
        $policy_id = $data['policy_id'] ?? '';
        $basic_plan_rider = $data['basic_plan_rider'] ?? '';
        $sum_covered = $data['sum_covered'] ?? '';
        $coverage_term = $data['coverage_term'] ?? '';
        $contribution = $data['contribution'] ?? '';
        $start_date = $data['start_date'] ?? date('Y-m-d');
        $end_date = $data['end_date'] ?? date('Y-m-d', strtotime('+1 year'));
        
        $title = $is_first ? "Policy Details" : "Additional Policy #" . $index;
        $remove_button = $is_first ? "" : '<button type="button" class="btn btn-danger btn-sm remove-policy" onclick="removePolicy(this)"><i class="fas fa-times"></i> Remove Policy</button>';
        
        $html = <<<HTML
        <div class="policy-form mt-4 pt-4 border-top" data-policy-index="$index" id="$form_id">
            <div class="policy-header d-flex justify-content-between align-items-center mb-3">
                <h6 class="mb-0">$title</h6>
                $remove_button
            </div>
            <div class="detail-row">
                <div class="detail-label">Assigned Agent <span class="required">*</span></div>
                <div class="detail-value">
                    <select class="form-control agent-select" name="agent_id_$index" required>
                        <option value="">Select Agent</option>
HTML;
        
        foreach ($agents as $agent) {
            $selected = ($agent_id == $agent['agent_id']) ? 'selected' : '';
            $html .= '<option value="' . htmlspecialchars($agent['agent_id']) . '" ' . $selected . '>';
            $html .= htmlspecialchars($agent['name']) . ' (' . htmlspecialchars($agent['agent_id']) . ')';
            $html .= '</option>';
        }
        
        $html .= <<<HTML
                    </select>
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Plan Type <span class="required">*</span></div>
                <div class="detail-value">
                    <input type="text" class="form-control" name="plan_type_$index" required value="$plan_type">
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Policy ID <span class="required">*</span></div>
                <div class="detail-value">
                    <input type="text" class="form-control" name="policy_id_$index" required value="$policy_id">
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Basic Plan/Rider <span class="required">*</span></div>
                <div class="detail-value">
                    <textarea class="form-control" name="basic_plan_rider_$index" required>$basic_plan_rider</textarea>
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Sum Covered <span class="required">*</span></div>
                <div class="detail-value">
                    <input type="number" step="0.01" class="form-control" name="sum_covered_$index" required value="$sum_covered">
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Coverage Term <span class="required">*</span></div>
                <div class="detail-value">
                    <input type="text" class="form-control" name="coverage_term_$index" required value="$coverage_term">
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Contribution <span class="required">*</span></div>
                <div class="detail-value">
                    <input type="number" step="0.01" class="form-control" name="contribution_$index" required value="$contribution">
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Start Date <span class="required">*</span></div>
                <div class="detail-value">
                    <input type="date" class="form-control" name="start_date_$index" required value="$start_date">
                </div>
            </div>
            <div class="detail-row">
                <div class="detail-label">End Date <span class="required">*</span></div>
                <div class="detail-value">
                    <input type="date" class="form-control" name="end_date_$index" required value="$end_date">
                </div>
            </div>
            <!-- Hidden input to indicate this is a policy form -->
            <input type="hidden" name="policy_index[]" value="$index">
        </div>
HTML;
        
        return $html;
    }
}

if (!function_exists('pm_processPolicyForms')) {
    // Function to process policy form submissions
    function pm_processPolicyForms() {
        // Get policy indices to find all policies in the form
        $policy_indices = $_POST['policy_index'] ?? [];
        $policies = [];
        
        // Process each policy form
        foreach ($policy_indices as $index) {
            $policies[] = [
                'agent_id' => $_POST["agent_id_{$index}"] ?? '',
                'plan_type' => $_POST["plan_type_{$index}"] ?? '',
                'policy_id' => $_POST["policy_id_{$index}"] ?? '',
                'basic_plan_rider' => $_POST["basic_plan_rider_{$index}"] ?? '',
                'sum_covered' => $_POST["sum_covered_{$index}"] ?? '',
                'coverage_term' => $_POST["coverage_term_{$index}"] ?? '',
                'contribution' => $_POST["contribution_{$index}"] ?? '',
                'start_date' => $_POST["start_date_{$index}"] ?? date('Y-m-d'),
                'end_date' => $_POST["end_date_{$index}"] ?? date('Y-m-d', strtotime('+1 year'))
            ];
        }
        
        return $policies;
    }
}

if (!function_exists('pm_validatePolicies')) {
    // Function to validate policies
    function pm_validatePolicies($policies) {
        $errors = [];
        
        if (empty($policies)) {
            $errors[] = "At least one policy is required";
        } else {
            foreach ($policies as $index => $policy) {
                if (empty($policy['agent_id'])) {
                    $errors[] = "Assigned agent is required for policy #" . ($index + 1);
                }
                if (empty($policy['plan_type'])) {
                    $errors[] = "Plan type is required for policy #" . ($index + 1);
                }
                if (empty($policy['policy_id'])) {
                    $errors[] = "Policy ID is required for policy #" . ($index + 1);
                }
                if (empty($policy['basic_plan_rider'])) {
                    $errors[] = "Basic Plan/Rider is required for policy #" . ($index + 1);
                }
                if (empty($policy['sum_covered'])) {
                    $errors[] = "Sum Covered is required for policy #" . ($index + 1);
                }
                if (empty($policy['coverage_term'])) {
                    $errors[] = "Coverage Term is required for policy #" . ($index + 1);
                }
                if (empty($policy['contribution'])) {
                    $errors[] = "Contribution is required for policy #" . ($index + 1);
                }
                if (empty($policy['start_date'])) {
                    $errors[] = "Start Date is required for policy #" . ($index + 1);
                }
                if (empty($policy['end_date'])) {
                    $errors[] = "End Date is required for policy #" . ($index + 1);
                }
            }
        }
        
        return $errors;
    }
}

if (!function_exists('pm_getPolicyFormScripts')) {
    // JavaScript for policy form management
    function pm_getPolicyFormScripts() {
        $js = <<<JAVASCRIPT
<script>
// Policy form management
window.onload = function() {
    console.log("Policy management scripts loaded");
    
    // Initialize Select2 if jQuery is available
    if (typeof $ !== 'undefined') {
        $('.agent-select').select2({
            placeholder: "Search agent by name or ID",
            allowClear: true,
            width: '100%'
        });
    }
    
    // Get the policy container and add policy button
    var policiesContainer = document.getElementById('policiesContainer');
    var addPolicyBtn = document.getElementById('addPolicyBtn');
    
    // Policy counter for dynamic IDs - start at 1 because 0 is the first form
    var policyCounter = 1;
    
    // Add click event using vanilla JavaScript
    if (addPolicyBtn) {
        addPolicyBtn.onclick = function() {
            console.log("Add Policy button clicked");
            
            // Make an AJAX request to get the HTML for a new policy form
            var xhr = new XMLHttpRequest();
            xhr.open('GET', 'get_policy_form.php?index=' + policyCounter, true);
            xhr.onload = function() {
                if (xhr.status === 200) {
                    // Create a temporary container
                    var temp = document.createElement('div');
                    temp.innerHTML = xhr.responseText;
                    
                    // Add the new form to the container
                    while (temp.firstChild) {
                        policiesContainer.appendChild(temp.firstChild);
                    }
                    
                    console.log("Added new policy form with index:", policyCounter);
                    
                    // Initialize Select2 for the new dropdown if jQuery is available
                    if (typeof $ !== 'undefined') {
                        try {
                            $('#policy_form_' + policyCounter + ' select').select2({
                                placeholder: "Search agent by name or ID",
                                allowClear: true,
                                width: '100%'
                            });
                        } catch(e) {
                            console.error("Error initializing Select2:", e);
                        }
                    }
                    
                    // Update counter
                    policyCounter++;
                } else {
                    console.error("Error loading policy form:", xhr.statusText);
                }
            };
            xhr.onerror = function() {
                console.error("Network error when trying to load policy form");
            };
            xhr.send();
            
            return false; // Prevent default button action
        };
    } else {
        console.error("Add Policy button not found!");
    }
};

// Global function to remove a policy element
function removePolicy(element) {
    var policyForm = element.closest('.policy-form');
    if (policyForm) {
        policyForm.parentNode.removeChild(policyForm);
        console.log("Policy removed");
    }
}
</script>
JAVASCRIPT;
        
        return $js;
    }
}
?> 