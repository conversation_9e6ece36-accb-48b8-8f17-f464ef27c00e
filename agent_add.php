<?php
// agent_add.php
session_start();
require_once 'config.php';

// Debug check for database connection
if (!isset($conn) || $conn === null) {
    die("Database connection failed. Please check your configuration.");
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Set page title
$pageTitle = 'ADD NEW AGENT';

// Initialize variables
$errors = [];
$success = false;
$formData = [
    'agent_id' => '',
    'name' => '',
    'ic_number' => '',
    'gender' => '',
    'date_of_birth' => '',
    'phone_number' => '',
    'email' => '',
    'address' => '',
    'beneficiary_phone' => '',
    'beneficiary_name' => '',
    'beneficiary_ic' => '',
    'work_experience' => ''
];
$educationData = []; // Initialize education data array

// If form is submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data and validate
    $agent_id = trim($_POST['agent_id'] ?? '');
    $name = trim($_POST['name'] ?? '');
    
    // Force numeric-only values using preg_replace
    $ic_number = preg_replace('/[^0-9]/', '', $_POST['ic_number'] ?? '');
    $phone_number = preg_replace('/[^0-9]/', '', $_POST['phone_number'] ?? '');
    $beneficiary_phone = preg_replace('/[^0-9]/', '', $_POST['beneficiary_phone'] ?? '');
    $beneficiary_ic = preg_replace('/[^0-9]/', '', $_POST['beneficiary_ic'] ?? '');
    
    $gender = $_POST['gender'] ?? '';
    $date_of_birth = $_POST['date_of_birth'] ?? '';
    $email = filter_input(INPUT_POST, 'email', FILTER_VALIDATE_EMAIL);
    $address = trim($_POST['address'] ?? '');
    $beneficiary_name = trim($_POST['beneficiary_name'] ?? '');
    $work_experience = trim($_POST['work_experience'] ?? '');
    
    // Store form data for repopulating the form
    $formData = [
        'agent_id' => $agent_id,
        'name' => $name,
        'ic_number' => $ic_number,
        'gender' => $gender,
        'date_of_birth' => $date_of_birth,
        'phone_number' => $phone_number,
        'email' => $_POST['email'] ?? '',
        'address' => $address,
        'beneficiary_phone' => $beneficiary_phone,
        'beneficiary_name' => $beneficiary_name,
        'beneficiary_ic' => $beneficiary_ic,
        'work_experience' => $work_experience
    ];
    
    // Validate required fields
    if (empty($agent_id)) {
        $errors[] = "Agent ID is required";
    } elseif (!preg_match('/^[A-Za-z0-9]+$/', $agent_id)) {
        $errors[] = "Agent ID can only contain letters and numbers";
    } elseif (strlen($agent_id) > 20) {
        $errors[] = "Agent ID cannot be longer than 20 characters";
    }
    
    if (empty($name)) {
        $errors[] = "Name is required";
    }
    
    if (empty($ic_number)) {
        $errors[] = "IC Number is required";
    }
    
    if (empty($gender)) {
        $errors[] = "Gender is required";
    } elseif (!in_array($gender, ['Male', 'Female', 'Other'])) {
        $errors[] = "Invalid gender value";
    }
    
    if (empty($date_of_birth)) {
        $errors[] = "Date of birth is required";
    }
    
    if (empty($phone_number)) {
        $errors[] = "Phone number is required";
    }
    
    if (empty($_POST['email'])) {
        $errors[] = "Email is required";
    } elseif (!$email) {
        $errors[] = "Please enter a valid email address";
    }
    
    if (empty($address)) {
        $errors[] = "Address is required";
    }
    
    // Check if agent ID already exists
    if (empty($errors) && checkAgentIdExists($agent_id, $conn)) {
        $errors[] = "Agent ID already exists";
    }
    
    // If no errors, insert into database
    if (empty($errors)) {
        try {
            // Handle photo upload first
            $photoPath = null;
            if (isset($_FILES['photo']) && $_FILES['photo']['error'] === UPLOAD_ERR_OK) {
                $uploadDir = 'uploads/photos/';
                if (!file_exists($uploadDir)) {
                    if (!mkdir($uploadDir, 0777, true)) {
                        throw new Exception('Failed to create upload directory');
                    }
                }
                
                // Validate file type
                $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
                $fileType = $_FILES['photo']['type'];
                
                if (!in_array($fileType, $allowedTypes)) {
                    throw new Exception('Invalid file type. Only JPG, PNG and GIF are allowed.');
                }
                
                $fileExtension = strtolower(pathinfo($_FILES['photo']['name'], PATHINFO_EXTENSION));
                $newFileName = 'photo_' . $agent_id . '_' . time() . '.' . $fileExtension;
                $targetPath = $uploadDir . $newFileName;
                
                if (!move_uploaded_file($_FILES['photo']['tmp_name'], $targetPath)) {
                    throw new Exception('Failed to upload photo.');
                }
                
                $photoPath = $targetPath;
            }

            // Create agent data array
            $agentData = [
                'agent_id' => $agent_id,
                'name' => $name,
                'ic_number' => $ic_number,
                'gender' => $gender,
                'date_of_birth' => $date_of_birth,
                'phone_number' => $phone_number,
                'email' => $email,
                'address' => $address,
                'beneficiary_phone' => $beneficiary_phone,
                'beneficiary_name' => $beneficiary_name,
                'beneficiary_ic' => $beneficiary_ic,
                'work_experience' => $work_experience,
                'photo' => $photoPath
            ];
            
            // Store education data if provided
            $educationData = [];
            if (!empty($_POST['education_level']) && is_array($_POST['education_level'])) {
                for ($i = 0; $i < count($_POST['education_level']); $i++) {
                    if (!empty($_POST['education_level'][$i])) {
                        $educationData[] = [
                            'level' => $_POST['education_level'][$i],
                            'year' => $_POST['education_year'][$i] ?? null,
                            'institution' => $_POST['education_institution'][$i] ?? null
                        ];
                    }
                }
            }
            
            // Insert agent data
            if (createNewAgent($agentData, $educationData, $conn)) {
                // Handle document uploads if any
                handleDocumentUploads($agent_id);
                
                // Set success message
                $success = true;
                
                // Redirect to agent list or view page after successful addition
                header('Location: agents.php?success=Agent added successfully');
                exit;
            } else {
                $errors[] = "Failed to add agent. Please try again.";
            }
        } catch (Exception $e) {
            $errors[] = $e->getMessage();
        }
    }
}

// Include layout header
include 'layout.php';
?>

<style>
.detail-container {
    max-width: 1000px;
    margin-left: 400px;
    margin-bottom: 50px;

    padding: 30px;
    background: #fff;
    box-shadow: 0 0 20px rgba(0,0,0,0.05);
    border-radius: 15px;
}

.agent-header {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 40px;
    text-align: center;
    color: #2c3e50;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.agent-info {
    display: flex;
    gap: 40px;
    margin-bottom: 40px;
    background: #f8f9fa;
    padding: 30px;
    border-radius: 12px;
}

.agent-photo {
    width: 150px;
    height: 200px;
    background-color: #e9ecef;
    flex-shrink: 0;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.agent-photo i {
    font-size: 48px;
    color: #adb5bd;
    margin-bottom: 10px;
}

.photo-upload-btn {
    font-size: 12px;
    padding: 6px 12px;
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 10px;
    transition: all 0.3s ease;
}

.photo-upload-btn:hover {
    background: #e9ecef;
}

.agent-details {
    flex-grow: 1;
}

.detail-row {
    margin-bottom: 15px;
    display: flex;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    width: 180px;
    font-weight: 600;
    color: #495057;
    padding-top: 8px;
}

.detail-value {
    flex-grow: 1;
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

textarea.form-control {
    min-height: 100px;
    resize: vertical;
}

.section-container {
    margin-top: 40px;
    background: #f8f9fa;
    padding: 25px;
    border-radius: 12px;
}

.section-title {
    font-weight: 600;
    font-size: 18px;
    color: #2c3e50;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e9ecef;
}

.document-section {
    margin-top: 40px;
    background: #f8f9fa;
    padding: 25px;
    border-radius: 12px;
}

.document-upload {
    display: flex;
    align-items: center;
    padding: 15px;
    margin-bottom: 15px;
    background: white;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.document-upload .doc-icon {
    margin-right: 15px;
    color: #e74c3c;
    font-size: 20px;
}

.document-upload .doc-info {
    flex-grow: 1;
}

.document-upload .doc-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 5px;
}

.document-upload .doc-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.doc-actions .btn-upload {
    padding: 6px 12px;
    background: #2ecc71;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.doc-actions button:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.doc-actions .file-input {
    display: none;
}

.btn-container {
    margin-top: 40px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.btn {
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #2c3e50;
    border: none;
    color: white;
}

.btn-primary:hover {
    background-color: #1e2a37;
    transform: translateY(-2px);
}

.btn-secondary {
    background-color: #6c757d;
    border: none;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
    transform: translateY(-2px);
}

.education-container {
    margin-top: 20px;
}

.education-row {
    background: white;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.03);
    border: 1px solid #dee2e6;
}

.education-fields {
    display: flex;
    gap: 15px;
    margin-bottom: 10px;
}

.education-field {
    flex: 1;
}

.btn-outline-primary {
    color: #2c3e50;
    background-color: transparent;
    border: 1px solid #2c3e50;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background-color: #2c3e50;
    color: white;
}

.alert {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 8px;
}

.alert-danger {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.required {
    color: #e74a3b;
    margin-left: 4px;
}

.btn-remove {
    background-color: #e74c3c;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 5px 10px;
    font-size: 12px;
    cursor: pointer;
}
</style>

<div class="detail-container">
    <?php if (!empty($errors)): ?>
        <div class="alert alert-danger">
            <ul class="mb-0">
                <?php foreach ($errors as $error): ?>
                    <li><?php echo htmlspecialchars($error); ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>
    
    <div class="agent-header">
        ADD NEW AGENT
    </div>

    <form method="POST" action="agent_add.php" enctype="multipart/form-data">
        <div class="agent-info">
            <div class="agent-photo">
                <i class="fas fa-user"></i>
                <input type="file" id="photo" name="photo" class="d-none">
                <label for="photo" class="photo-upload-btn">Upload Photo</label>
            </div>

            <div class="agent-details">
                <div class="detail-row">
                    <div class="detail-label">Agent ID <span class="required">*</span></div>
                    <div class="detail-value">
                        <input type="text" class="form-control" name="agent_id" required value="<?php echo htmlspecialchars($formData['agent_id']); ?>">
                    </div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Name <span class="required">*</span></div>
                    <div class="detail-value">
                        <input type="text" class="form-control" name="name" required value="<?php echo htmlspecialchars($formData['name']); ?>">
                    </div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">IC Number <span class="required">*</span></div>
                    <div class="detail-value">
                        <input type="tel" class="form-control strict-numbers-only" name="ic_number" required value="<?php echo htmlspecialchars($formData['ic_number']); ?>" pattern="[0-9]*" inputmode="numeric" onkeypress="return event.charCode >= 48 && event.charCode <= 57" maxlength="20">
                    </div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Gender <span class="required">*</span></div>
                    <div class="detail-value">
                        <select class="form-control" name="gender" required>
                            <option value="">Select Gender</option>
                            <option value="Male" <?php echo $formData['gender'] === 'Male' ? 'selected' : ''; ?>>Male</option>
                            <option value="Female" <?php echo $formData['gender'] === 'Female' ? 'selected' : ''; ?>>Female</option>
                        </select>
                    </div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Date Of Birth <span class="required">*</span></div>
                    <div class="detail-value">
                        <input type="date" class="form-control" name="date_of_birth" required value="<?php echo htmlspecialchars($formData['date_of_birth']); ?>">
                    </div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Phone Number <span class="required">*</span></div>
                    <div class="detail-value">
                        <input type="tel" class="form-control strict-numbers-only" name="phone_number" required value="<?php echo htmlspecialchars($formData['phone_number']); ?>" pattern="[0-9]*" inputmode="numeric" onkeypress="return event.charCode >= 48 && event.charCode <= 57" maxlength="15">
                    </div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Email <span class="required">*</span></div>
                    <div class="detail-value">
                        <input type="email" class="form-control" name="email" required value="<?php echo htmlspecialchars($formData['email']); ?>">
                    </div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Address <span class="required">*</span></div>
                    <div class="detail-value">
                        <textarea class="form-control" name="address" required><?php echo htmlspecialchars($formData['address']); ?></textarea>
                    </div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Beneficiary's Phone</div>
                    <div class="detail-value">
                        <input type="tel" class="form-control strict-numbers-only" name="beneficiary_phone" value="<?php echo htmlspecialchars($formData['beneficiary_phone']); ?>" pattern="[0-9]*" inputmode="numeric" onkeypress="return event.charCode >= 48 && event.charCode <= 57" maxlength="15">
                    </div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Beneficiary's Name</div>
                    <div class="detail-value">
                        <input type="text" class="form-control" name="beneficiary_name" value="<?php echo htmlspecialchars($formData['beneficiary_name']); ?>">
                    </div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Beneficiary's IC</div>
                    <div class="detail-value">
                        <input type="tel" class="form-control strict-numbers-only" name="beneficiary_ic" value="<?php echo htmlspecialchars($formData['beneficiary_ic']); ?>" pattern="[0-9]*" inputmode="numeric" onkeypress="return event.charCode >= 48 && event.charCode <= 57" maxlength="20">
                    </div>
                </div>
                <div class="detail-row">
                    <div class="detail-label">Work Experience</div>
                    <div class="detail-value">
                        <textarea class="form-control" name="work_experience"><?php echo htmlspecialchars($formData['work_experience']); ?></textarea>
                    </div>
                </div>
            </div>
        </div>

        <div class="section-container">
            <div class="section-title">Education Details</div>
            
            <div id="education-container" class="education-container">
                <?php if (empty($educationData)): ?>
                <div class="education-row">
                    <div class="education-fields">
                        <div class="education-field">
                            <label class="form-label">Education Level</label>
                            <input type="text" class="form-control" name="education_level[]" placeholder="e.g. SPM, Diploma, Degree">
                        </div>
                        <div class="education-field">
                            <label class="form-label">Year Completed</label>
                            <input type="number" class="form-control" name="education_year[]" placeholder="Year">
                        </div>
                        <div class="education-field">
                            <label class="form-label">Institution</label>
                            <input type="text" class="form-control" name="education_institution[]" placeholder="Institution Name">
                        </div>
                    </div>
                </div>
                <?php else: ?>
                    <?php foreach ($educationData as $index => $edu): ?>
                    <div class="education-row">
                        <div class="education-fields">
                            <div class="education-field">
                                <label class="form-label">Education Level</label>
                                <input type="text" class="form-control" name="education_level[]" 
                                       value="<?php echo htmlspecialchars($edu['level']); ?>" 
                                       placeholder="e.g. SPM, Diploma, Degree">
                            </div>
                            <div class="education-field">
                                <label class="form-label">Year Completed</label>
                                <input type="number" class="form-control" name="education_year[]" 
                                       value="<?php echo htmlspecialchars($edu['year']); ?>"
                                       placeholder="Year">
                            </div>
                            <div class="education-field">
                                <label class="form-label">Institution</label>
                                <input type="text" class="form-control" name="education_institution[]" 
                                       value="<?php echo htmlspecialchars($edu['institution']); ?>"
                                       placeholder="Institution Name">
                            </div>
                        </div>
                        <?php if ($index > 0): // Only show remove button for additional education rows ?>
                        <button type="button" class="btn-remove remove-education">
                            <i class="fas fa-times"></i> Remove
                        </button>
                        <?php endif; ?>
                    </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
            
            <button type="button" class="btn-outline-primary" id="add-education">
                <i class="fas fa-plus"></i> Add More Education
            </button>
        </div>

        <div class="document-section">
            <div class="section-title">Documents</div>
            
            <!-- Education Certificate -->
            <div class="document-upload">
                <i class="fas fa-file-pdf doc-icon"></i>
                <div class="doc-info">
                    <div class="doc-title">Education Certificate</div>
                    <div class="doc-filename" id="education_cert_name"></div>
                </div>
                <div class="doc-actions">
                    <label class="btn-upload">
                        <i class="fas fa-upload"></i> Upload File
                        <input type="file" name="documents[education_cert]" class="file-input" accept=".pdf,.jpg,.jpeg,.png" onchange="updateFileName(this, 'education_cert_name')">
                    </label>
                </div>
            </div>

            <!-- NRIC Copy -->
            <div class="document-upload">
                <i class="fas fa-file-pdf doc-icon"></i>
                <div class="doc-info">
                    <div class="doc-title">Photocopy of NRIC</div>
                    <div class="doc-filename" id="nric_name"></div>
                </div>
                <div class="doc-actions">
                    <label class="btn-upload">
                        <i class="fas fa-upload"></i> Upload File
                        <input type="file" name="documents[nric]" class="file-input" accept=".pdf,.jpg,.jpeg,.png" onchange="updateFileName(this, 'nric_name')">
                    </label>
                </div>
            </div>

            <!-- Beneficiary's NRIC -->
            <div class="document-upload">
                <i class="fas fa-file-pdf doc-icon"></i>
                <div class="doc-info">
                    <div class="doc-title">Photocopy of Beneficiary's NRIC</div>
                    <div class="doc-filename" id="beneficiary_nric_name"></div>
                </div>
                <div class="doc-actions">
                    <label class="btn-upload">
                        <i class="fas fa-upload"></i> Upload File
                        <input type="file" name="documents[beneficiary_nric]" class="file-input" accept=".pdf,.jpg,.jpeg,.png" onchange="updateFileName(this, 'beneficiary_nric_name')">
                    </label>
                </div>
            </div>
        </div>

        <div class="btn-container">
            <a href="agents.php" class="btn btn-secondary">
                Cancel
            </a>
            <button type="submit" class="btn btn-primary">
                Save Agent
            </button>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Photo preview
    const photoInput = document.getElementById('photo');
    photoInput.addEventListener('change', function(e) {
        if (this.files && this.files[0]) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const photoContainer = document.querySelector('.agent-photo');
                const icon = photoContainer.querySelector('i');
                if (icon) {
                    icon.style.display = 'none';
                }
                
                let preview = photoContainer.querySelector('.photo-preview');
                if (!preview) {
                    preview = document.createElement('img');
                    preview.className = 'photo-preview';
                    preview.style.width = '100%';
                    preview.style.height = '80%';
                    preview.style.objectFit = 'cover';
                    preview.style.borderRadius = '4px';
                    preview.style.marginBottom = '10px';
                    photoContainer.insertBefore(preview, photoContainer.firstChild);
                }
                
                preview.src = e.target.result;
            }
            reader.readAsDataURL(this.files[0]);
        }
    });
    
    // Add more education fields
    document.getElementById('add-education').addEventListener('click', function() {
        const container = document.getElementById('education-container');
        const newRow = document.createElement('div');
        newRow.className = 'education-row';
        
        newRow.innerHTML = `
            <div class="education-fields">
                <div class="education-field">
                    <label class="form-label">Education Level</label>
                    <input type="text" class="form-control" name="education_level[]" placeholder="e.g. SPM, Diploma, Degree">
                </div>
                <div class="education-field">
                    <label class="form-label">Year Completed</label>
                    <input type="number" class="form-control" name="education_year[]" placeholder="Year">
                </div>
                <div class="education-field">
                    <label class="form-label">Institution</label>
                    <input type="text" class="form-control" name="education_institution[]" placeholder="Institution Name">
                </div>
            </div>
            <button type="button" class="btn-remove remove-education">
                <i class="fas fa-times"></i> Remove
            </button>
        `;
        
        container.appendChild(newRow);
        
        // Add event listener to remove button
        newRow.querySelector('.remove-education').addEventListener('click', function() {
            container.removeChild(newRow);
        });
    });
    
    // STRICT NUMBERS-ONLY VALIDATION - MULTIPLE LAYERS OF PROTECTION
    document.querySelectorAll('.strict-numbers-only').forEach(function(input) {
        // Layer 1: Clear any non-numeric characters on load
        input.value = input.value.replace(/[^0-9]/g, '');
        
        // Layer 2: Input event listener to immediately remove non-numeric chars
        input.addEventListener('input', function(e) {
            const numericValue = this.value.replace(/[^0-9]/g, '');
            if (this.value !== numericValue) {
                this.value = numericValue;
            }
        });
        
        // Layer 3: Keydown event to prevent entering non-numeric chars
        input.addEventListener('keydown', function(e) {
            // Allow: backspace, delete, tab, escape, enter, navigation
            if ([46, 8, 9, 27, 13, 37, 38, 39, 40].indexOf(e.keyCode) !== -1 ||
                // Allow: Ctrl+A, Ctrl+C, Ctrl+X, Ctrl+V
                (e.keyCode === 65 && e.ctrlKey === true) ||
                (e.keyCode === 67 && e.ctrlKey === true) ||
                (e.keyCode === 88 && e.ctrlKey === true) ||
                // Allow: home, end
                (e.keyCode >= 35 && e.keyCode <= 39)) {
                // let it happen, don't do anything
                return;
            }
            
            // Ensure that it's a number and stop the keypress if not
            if ((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) && 
                (e.keyCode < 96 || e.keyCode > 105)) {
                e.preventDefault();
            }
        });
        
        // Layer 4: Paste event to filter and clean pasted content
        input.addEventListener('paste', function(e) {
            e.preventDefault();
            const pastedText = (e.clipboardData || window.clipboardData).getData('text');
            this.value = this.value + pastedText.replace(/[^0-9]/g, '');
        });
        
        // Layer 5: Focus event to validate content when field gets focus
        input.addEventListener('focus', function() {
            this.value = this.value.replace(/[^0-9]/g, '');
        });
        
        // Layer 6: Blur event to validate when leaving the field
        input.addEventListener('blur', function() {
            this.value = this.value.replace(/[^0-9]/g, '');
        });
    });
});

function updateFileName(input, displayId) {
    const fileName = input.files[0]?.name;
    if (fileName) {
        document.getElementById(displayId).textContent = 'Selected: ' + fileName;
    }
}
</script>

<?php include 'layout_footer.php'; ?> 

<?php
// Function to add agent and related data - renamed to avoid conflict with function in config.php
function createNewAgent($agentData, $educationData, $connection) {
    // Check if $conn is valid
    if (!$connection) {
        error_log("Database connection not available in createNewAgent function");
        return false;
    }
    
    try {
        // Start transaction
        $connection->begin_transaction();
        
        // Insert into agents table
        $stmt = $connection->prepare("INSERT INTO agents (agent_id, name, ic_number, gender, date_of_birth, 
            phone_number, email, address, beneficiary_phone, work_experience, photo) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
            
        $stmt->bind_param("sssssssssss", 
            $agentData['agent_id'],
            $agentData['name'],
            $agentData['ic_number'],
            $agentData['gender'],
            $agentData['date_of_birth'],
            $agentData['phone_number'],
            $agentData['email'],
            $agentData['address'],
            $agentData['beneficiary_phone'],
            $agentData['work_experience'],
            $agentData['photo']
        );
        
        if (!$stmt->execute()) {
            throw new Exception("Error inserting agent data: " . $stmt->error);
        }
        
        // Insert education details if provided
        if (!empty($educationData)) {
            $eduStmt = $connection->prepare("INSERT INTO educationdetails (agent_id, level, year_completed, institution_name) 
                VALUES (?, ?, ?, ?)");
                
            foreach ($educationData as $edu) {
                if (!empty($edu['level'])) {
                    $eduStmt->bind_param("ssss", 
                        $agentData['agent_id'],
                        $edu['level'],
                        $edu['year'],
                        $edu['institution']
                    );
                    
                    if (!$eduStmt->execute()) {
                        throw new Exception("Error inserting education data: " . $eduStmt->error);
                    }
                }
            }
            $eduStmt->close();
        }
        
        // Commit transaction
        $connection->commit();
        return true;
        
    } catch (Exception $e) {
        // Rollback transaction on error
        $connection->rollback();
        error_log("Error adding agent: " . $e->getMessage());
        return false;
    }
}

// Function to check if agent ID exists
function checkAgentIdExists($agent_id, $connection) {
    $stmt = $connection->prepare("SELECT agent_id FROM agents WHERE agent_id = ?");
    $stmt->bind_param("s", $agent_id);
    $stmt->execute();
    $result = $stmt->get_result();
    return $result->num_rows > 0;
}

// Function to check if agent ID exists - only define if it doesn't already exist
if (!function_exists('agentIdExists')) {
    function agentIdExists($agent_id) {
        global $conn;
        $stmt = $conn->prepare("SELECT agent_id FROM agents WHERE agent_id = ?");
        $stmt->bind_param("s", $agent_id);
        $stmt->execute();
        $result = $stmt->get_result();
        return $result->num_rows > 0;
    }
} 