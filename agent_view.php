<?php
// agent_view.php
session_start();
require_once 'config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Check if agent ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header('Location: agents.php?error=Invalid agent ID');
    exit;
}

$agentId = $_GET['id'];
$agent = getAgentById($agentId);

if (!$agent) {
    header('Location: agents.php?error=Agent not found');
    exit;
}

// Get agent's education details
$education = getAgentEducation($agentId);

// Get agent's documents
$documents = getAgentDocuments($agentId);

// Set page title
$pageTitle = 'AGENT DETAILS';

// Include layout header
include 'layout.php';
?>

<style>
.detail-container {
    max-width: 1000px;
    margin-left: 400px;
    margin-bottom: 50px;
    padding: 30px;
    background: #fff;
    box-shadow: 0 0 20px rgba(0,0,0,0.05);
    border-radius: 15px;
}

.agent-header {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 40px;
    text-align: center;
    color: #2c3e50;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.agent-info {
    display: flex;
    gap: 40px;
    margin-bottom: 40px;
    background: #f8f9fa;
    padding: 30px;
    border-radius: 12px;
}

.agent-photo {
    width: 150px;  /* Passport photo width */
    height: 200px; /* Passport photo height */
    background-color: #e9ecef;
    flex-shrink: 0;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.agent-photo i {
    font-size: 48px;
    color: #adb5bd;
}

.agent-details {
    flex-grow: 1;
}

.detail-row {
    margin-bottom: 15px;
    display: flex;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    width: 180px;
    font-weight: 600;
    color: #495057;
}

.detail-value {
    flex-grow: 1;
    color: #2c3e50;
}

.section-container {
    margin-top: 40px;
    background: #f8f9fa;
    padding: 25px;
    border-radius: 12px;
}

.section-title {
    font-weight: 600;
    font-size: 18px;
    color: #2c3e50;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e9ecef;
}

.education-table, .client-table {
    width: 100%;
    margin-top: 15px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 0 10px rgba(0,0,0,0.05);
}

.education-table th, .client-table th {
    background-color: #2c3e50;
    color: white;
    padding: 15px;
    font-weight: 500;
}

.education-table td, .client-table td {
    padding: 12px 15px;
    background-color: white;
    border-bottom: 1px solid #eee;
}

.education-table tr:last-child td {
    border-bottom: none;
}

.document-section {
    margin-top: 40px;
    background: #f8f9fa;
    padding: 25px;
    border-radius: 12px;
}

.document-link {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    margin-bottom: 10px;
    background: white;
    border-radius: 8px;
    color: #0066cc;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 1px solid #dee2e6;
}

.document-link:hover {
    background: #f8f9fa;
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.document-link i {
    margin-right: 10px;
    color: #e74c3c;
}
</style>

<div class="detail-container">
    <div class="agent-header">
        AGENT DETAILS
                </div>
                
    <div class="agent-info">
        <div class="agent-photo">
            <?php if (!empty($agent['photo']) && file_exists($agent['photo'])): ?>
                <img src="<?php echo htmlspecialchars($agent['photo']); ?>" alt="Agent Photo" style="width: 100%; height: 100%; object-fit: cover; border-radius: 8px;">
            <?php else: ?>
                <i class="fas fa-user"></i>
            <?php endif; ?>
        </div>
                
        <div class="agent-details">
            <div class="detail-row">
                <div class="detail-label">Name :</div>
                <div class="detail-value"><?php echo htmlspecialchars($agent['name']); ?></div>
                </div>
            <div class="detail-row">
                <div class="detail-label">IC Number :</div>
                <div class="detail-value"><?php echo htmlspecialchars($agent['ic_number']); ?></div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Agent ID :</div>
                <div class="detail-value"><?php echo htmlspecialchars($agent['agent_id']); ?></div>
        </div>
            <div class="detail-row">
                <div class="detail-label">Gender :</div>
                <div class="detail-value"><?php echo htmlspecialchars($agent['gender']); ?></div>
    </div>
            <div class="detail-row">
                <div class="detail-label">Date Of Birth :</div>
                <div class="detail-value"><?php echo htmlspecialchars($agent['date_of_birth']); ?></div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Phone Number :</div>
                <div class="detail-value"><?php echo htmlspecialchars($agent['phone_number']); ?></div>
                    </div>
            <div class="detail-row">
                <div class="detail-label">Email :</div>
                <div class="detail-value"><?php echo htmlspecialchars($agent['email']); ?></div>
                </div>
            <div class="detail-row">
                <div class="detail-label">Address :</div>
                <div class="detail-value"><?php echo htmlspecialchars($agent['address']); ?></div>
                    </div>
            <div class="detail-row">
                <div class="detail-label">Beneficiary's Phone :</div>
                <div class="detail-value"><?php echo htmlspecialchars($agent['beneficiary_phone']); ?></div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Beneficiary's Name :</div>
                <div class="detail-value"><?php echo htmlspecialchars($agent['beneficiary_name'] ?? ''); ?></div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Beneficiary's IC :</div>
                <div class="detail-value"><?php echo htmlspecialchars($agent['beneficiary_ic'] ?? ''); ?></div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Work Experience :</div>
                <div class="detail-value"><?php echo htmlspecialchars($agent['work_experience']); ?></div>
            </div>
            </div>
        </div>
        
    <div class="section-container">
        <div class="section-title">Education Details</div>
        <table class="education-table">
            <thead>
                <tr>
                    <th>Level</th>
                    <th>Year Completed</th>
                    <th>Institution Name</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($education)): ?>
                    <tr>
                        <td colspan="3" style="text-align: center;">No education details available</td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($education as $edu): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($edu['level']); ?></td>
                            <td><?php echo htmlspecialchars($edu['year_completed']); ?></td>
                            <td><?php echo htmlspecialchars($edu['institution_name']); ?></td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>

    <div class="section-container">
        <div class="section-title">Client List</div>
        <table class="client-table">
            <thead>
                <tr>
                    <th>No.</th>
                    <th>Name</th>
                    <th>Client ID</th>
                    <th>Policy Number</th>
                                </tr>
            </thead>
            <tbody>
                <?php
                $clients = getAgentClients($agentId);
                if (!empty($clients)):
                    foreach ($clients as $index => $client):
                ?>
                    <tr>
                        <td><?php echo $index + 1; ?></td>
                        <td><?php echo htmlspecialchars($client['name']); ?></td>
                        <td><?php echo htmlspecialchars($client['client_id']); ?></td>
                        <td><?php echo htmlspecialchars($client['policy_number']); ?></td>
                    </tr>
                <?php
                    endforeach;
                endif;
                ?>
            </tbody>
        </table>
    </div>

    <div class="document-section">
        <div class="section-title">Documents</div>
        <?php if (empty($documents)): ?>
            <p>No documents uploaded.</p>
        <?php else: ?>
            <?php foreach ($documents as $document): ?>
                <a href="<?php echo htmlspecialchars($document['file_path']); ?>" class="document-link" target="_blank">
                    <i class="fas fa-file-pdf doc-icon"></i>
                    <?php echo htmlspecialchars($document['type_name']); ?>: <?php echo htmlspecialchars($document['file_name']); ?>
                </a>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
</div>

<?php include 'layout_footer.php'; ?>